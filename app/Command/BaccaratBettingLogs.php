<?php

declare(strict_types=1);

namespace App\Command;

use App\Baccarat\Model\BaccaratSimulatedBettingLog;
use App\Baccarat\Model\BaccaratSimulatedBetting;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Hyperf\Database\Model\Collection;
use Psr\Container\ContainerInterface;
use App\Baccarat\Listener\BettingBaccListener;

#[Command]
class BaccaratBettingLogs extends HyperfCommand
{

    public const DEFAULT_CONFIDENCE = 60;

    protected int $confidence;
    public function __construct(
        protected ContainerInterface $container,
    ) {
        parent::__construct('baccarat:betting:logs');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('Process baccarat betting based on lottery records with pagination support');

        
    }

    public function handle(): void
    {
        $startTime = microtime(true);

        try {
            $this->process();

            $duration = number_format(microtime(true) - $startTime, 8);
            $this->info("Baccarat betting completed successfully. Duration: {$duration}s");
        } catch (\Throwable $e) {
            $this->error("Error processing baccarat betting: " . $e->getMessage());
        }
    }

    protected function process(): void
    {
       
        BaccaratSimulatedBettingLog::where('betting_id', BettingBaccListener::BETTING_IDS)
            ->with('baccaratLotteryLog')
            ->orderBy('id')
            ->whereIn('betting_result', [0, 1])
            ->chunk(100, function (Collection $bettingLogs){

             

                $sequence = $bettingLogs->pluck('betting_result')->implode('');

                //如何获取 sequence 中 连续出现 0 的次数,注意是连续出现 0,例如 10000001110000 连续出现 0 的次数为  6 
                preg_match_all('/0+/', $sequence, $matches);
                $maxConsecutiveZeros = 0;
                if (!empty($matches[0])) {
                    foreach ($matches[0] as $match) {
                        $consecutiveZeros = strlen($match);
                        if ($consecutiveZeros > $maxConsecutiveZeros) {
                            $maxConsecutiveZeros = $consecutiveZeros;
                        }
                    }
                }

                $totalBets = $bettingLogs->count();
                $count0 = $bettingLogs->where('betting_result', 0)->count();
                $count1 = $bettingLogs->where('betting_result', 1)->count();

                // 检查是否有数据，避免除零错误 
                //保留两位小数
                $percentage0 = round($count0 / $totalBets * 100,2);
                $percentage1 = round($count1 / $totalBets * 100,2);
                $this->info("{$sequence} Total Bets: {$totalBets} Count of 0: {$count0} ({$percentage0}%) Count of 1: {$count1} ({$percentage1}%),maxConsecutiveZeros:{$maxConsecutiveZeros}");

                // $this->call('baccarat:random-sequence', [
                //     '--sequence' => $sequence,
                // ]);
            });
    }
}
