<?php

declare(strict_types=1);

namespace App\Command;

use App\Baccarat\Service\Prediction\PredictionGateway;
use App\Baccarat\Model\BaccaratLotteryLog;
use App\Baccarat\Service\LotteryResult; // 如需回退解析可启用
use Hyperf\Command\Annotation\Command;
use Hyperf\Command\Command as HyperfCommand;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputOption;
use Hyperf\Collection\Collection;

#[Command]
class BaccaratPredict extends HyperfCommand
{
    public function __construct(private readonly PredictionGateway $gateway)
    {
        parent::__construct('baccarat:predict');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('基于剩余牌组的百家乐概率预测');

        $this->addOption('deck', 'd', InputOption::VALUE_OPTIONAL, '从 terrace_deck_id 拉取开奖记录并构造 rounds');
        $this->addOption('samples', null, InputOption::VALUE_OPTIONAL, '蒙特卡洛模拟次数', 50000);
        $this->addOption('workers', 'w', InputOption::VALUE_OPTIONAL, '并发协程数（需Swoole）', 8);
        $this->addOption('min-penetration', null, InputOption::VALUE_OPTIONAL, '最小穿透度阈值(0-1)', 0.25);
        $this->addOption('z-threshold', null, InputOption::VALUE_OPTIONAL, '最小显著性阈值（max|z|）', 2.0);
    }

    public function handle(): void
    {


        $deckId = (int) ($this->input->getOption('deck') ?? 0);

        if (!$deckId) {
            $this->error('请提供 --deck=ID');
            return;
        }

        // 覆盖配置（从命令行参数）
        $this->gateway
            ->setSamples((int)$this->input->getOption('samples'))
            ->setWorkers((int)$this->input->getOption('workers'))
            ->setMinPenetration((float)$this->input->getOption('min-penetration'))
            ->setZThreshold((float)$this->input->getOption('z-threshold'));



            BaccaratLotteryLog::select('terrace_deck_id')
            ->distinct()
            ->orderBy('terrace_deck_id')
            ->chunk(100, function (Collection $logs) {
                $logs->each(function (BaccaratLotteryLog $log) {
                    $this->getSequence($log->terrace_deck_id);
                    
                });
            });

        
    }


    function getSequence(int $deckId){
        
        $data = new class {

            public function __construct(
                public array $rounds = [],
                public string $result = '',
                public string $sequence = '',
            ) {}

            public function setRound(array $round): void
            {
                $this->rounds = $round;
            }


        };


        BaccaratLotteryLog::query()
            ->where('terrace_deck_id', $deckId)
            ->orderBy('id')
            ->get()
            ->reduce(function (object $data, BaccaratLotteryLog $log) {

                //判断是否有预测
                if ($data->result) {

                    if (
                        ($data->result === 'banker' && $log->transformationResult === 'B') ||
                        ($data->result === 'player' && $log->transformationResult === 'P')
                    ) {
                        $data->sequence .= 1;
                    } else {
                        $data->sequence .= 0;
                    }

                    $data->result = '';
                }

                $lr = $log->getLotteryResult();
                $player = $this->extractFaces($lr->getPlayerHand());
                $banker = $this->extractFaces($lr->getBankerHand());
                $data->setRound(array_merge($data->rounds, [['player' => $player, 'banker' => $banker]]));

                //下一句预测
                //获取预测结构
                //假设这里是第一手，那么 $result 返回的应该是第二手的预测，
                $result = $this->gateway->predict($data->rounds)->toArray();

                //设置下一句的预测数据
                $data->result = $result['suggest']['side'] ?? '';

                //渲染预测结果
                // $this->renderResult($result);


                return $data;
            }, $data);


            $this->info('预测序列: ' . $data->sequence);
    }





    /** @param string[] $hand 输入如 ["S.10","H.K", ...] */
    private function extractFaces(array $hand): array
    {
        $faces = [];
        foreach ($hand as $card) {
            if (!is_string($card) || $card === '') continue;
            $pos = strrpos($card, '.');
            $face = $pos === false ? $card : substr($card, $pos + 1);
            $faces[] = strtoupper(trim($face));
        }
        return $faces;
    }

    private function renderResult(array $result): void
    {
        if (isset($result['error'])) {
            $this->error($result['message'] ?? 'Unknown error');
            return;
        }

        $table = new Table($this->output);
        $table->setHeaders(['项目', '值']);

        $rows = [
            ['穿透度', $this->formatPercentage($result['penetration'] ?? 0)],
            ['max|z|', $this->formatFloat($result['zMax'] ?? 0, 2)],
            ['是否触发', $result['triggered'] ? 'yes' : 'no'],
        ];

        if ($result['triggered']) {
            $probabilities = $result['probabilities'] ?? [];
            $conditioned = $result['conditioned'] ?? [];
            $ev = $result['ev'] ?? [];
            $suggest = $result['suggest'] ?? [];

            $rows = array_merge($rows, [
                ['pB(庄)', $this->formatFloat($probabilities['banker'] ?? 0, 4)],
                ['pP(闲)', $this->formatFloat($probabilities['player'] ?? 0, 4)],
                ['pT(和)', $this->formatFloat($probabilities['tie'] ?? 0, 4)],
                ['条件pB', $this->formatFloat($conditioned['banker'] ?? 0, 4)],
                ['条件pP', $this->formatFloat($conditioned['player'] ?? 0, 4)],
                ['EV_B', $this->formatFloat($ev['banker'] ?? 0, 4)],
                ['EV_P', $this->formatFloat($ev['player'] ?? 0, 4)],
                ['建议', sprintf(
                    '%s (edge=%s)',
                    $suggest['side'] ?? '-',
                    $this->formatFloat($suggest['edge'] ?? 0, 4)
                )],
            ]);
        }

        $table->addRows($rows);
        $table->render();
    }

    private function formatPercentage(float $value): string
    {
        return sprintf('%.2f%%', $value * 100);
    }

    private function formatFloat(float $value, int $precision): string
    {
        return sprintf("%.{$precision}f", $value);
    }
}
