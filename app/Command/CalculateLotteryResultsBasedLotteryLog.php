<?php

declare(strict_types=1);

namespace App\Command;

use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Psr\Container\ContainerInterface;
use App\Baccarat\Service\Platform\Bacc\Bacc;
use App\Baccarat\Service\BettingCalculator;
use App\Baccarat\Service\BettingAnalysisService;
use App\Baccarat\Service\BettingAnalysisOutputService;

/**
 * 百家乐投注结果计算命令
 * 
 * 基于历史开奖序列分析投注策略，提供详细的统计报告和建议
 * 支持缓存优化，避免重复计算相同序列
 */
#[Command]
class CalculateLotteryResultsBasedLotteryLog extends HyperfCommand
{
    // 默认配置常量
    private const DEFAULT_CONFIDENCE_THRESHOLD = 70;

    public function __construct(
        protected ContainerInterface $container,
        protected BettingAnalysisService $analysisService,
        protected BettingAnalysisOutputService $outputService
    ) {
        parent::__construct('calculate:lottery-results-based-lottery-log');
    }

    /**
     * 配置命令选项
     */
    public function configure(): void
    {
        parent::configure();
        $this->setDescription('根据开奖结果计算中奖结果')
            ->addOption(
                'sequence',
                's',
                \Symfony\Component\Console\Input\InputOption::VALUE_REQUIRED,
                '自定义序列 (例如: BBPPBPBP, 只支持 B、P、T)',
                null
            )
            ->addOption(
                'export-report',
                'e',
                \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL,
                '导出详细分析报告到文件 (例如: report.json)',
                false
            )
            ->addOption(
                'verbose-output',
                'd',
                \Symfony\Component\Console\Input\InputOption::VALUE_NONE,
                '显示详细的预测过程信息'
            )
            ->addOption(
                'mine-confidence',
                'c',
                \Symfony\Component\Console\Input\InputOption::VALUE_REQUIRED,
                '设置预测的最低置信度 (0-100)',
                self::DEFAULT_CONFIDENCE_THRESHOLD
            )
            ->addOption(
                'clear-cache',
                null,
                \Symfony\Component\Console\Input\InputOption::VALUE_NONE,
                '清除当前序列的分析缓存'
            );
    }

    /**
     * 命令执行入口
     */
    public function handle(): int
    {
        try {
            // 1. 获取并验证参数
            $sequence = $this->getSequenceOption();
            $confidenceThreshold = $this->getConfidenceThresholdOption();
            $enableVerbose = $this->input->getOption('verbose-output');
            $clearCache = $this->input->getOption('clear-cache');

            // 2. 处理缓存清除请求
            if ($clearCache) {
                if ($this->analysisService->clearSequenceCache($sequence, $confidenceThreshold)) {
                    $this->info("✅ 已清除序列缓存");
                } else {
                    $this->warn("⚠️  缓存清除失败或缓存不存在");
                }
            }

            // 3. 执行分析
            $this->info("开始分析投注序列...");
            $result = $this->analysisService->analyzeSequence(
                $sequence,
                $confidenceThreshold,
                $enableVerbose
            );

            // 4. 输出结果
            $this->outputAnalysisResult($result);

            // 5. 导出报告（如果需要）
            $this->exportReportIfRequested($result);

            $this->info("\n✅ 分析完成!");
            return self::SUCCESS;
        } catch (\InvalidArgumentException $e) {
            $this->error("参数错误: {$e->getMessage()}");
            return self::FAILURE;
        } catch (\Exception $e) {
            $this->error("执行错误: {$e->getMessage()}");
            return self::FAILURE;
        }
    }

    /**
     * 获取序列选项
     */
    private function getSequenceOption(): string
    {
        $sequence = $this->input->getOption('sequence');

        if (empty($sequence)) {
            throw new \InvalidArgumentException('请提供有效的序列参数 (使用 --sequence 或 -s)');
        }

        return (string) $sequence;
    }

    /**
     * 获取置信度阈值选项
     */
    private function getConfidenceThresholdOption(): int
    {
        $threshold = (int) $this->input->getOption('mine-confidence');

        if ($threshold < 0 || $threshold > 100) {
            throw new \InvalidArgumentException('置信度阈值必须在 0-100 之间');
        }

        return $threshold;
    }

    /**
     * 输出分析结果
     */
    private function outputAnalysisResult($result): void
    {
        // 输出基础结果
        foreach ($this->outputService->outputBasicResults($result) as $line) {
            $this->info($line);
        }

        // 输出详细分析
        foreach ($this->outputService->outputDetailedAnalysis($result) as $line) {
            $this->info($line);
        }

        // 输出建议
        foreach ($this->outputService->outputRecommendations($result) as $line) {
            $this->info($line);
        }
    }

    /**
     * 导出报告（如果需要）
     */
    private function exportReportIfRequested($result): void
    {
        $exportOption = $this->input->getOption('export-report');

        if ($exportOption) {
            $filename = is_string($exportOption) ? $exportOption : 'betting_analysis_report.json';

            if ($this->analysisService->exportReport($result, $filename)) {
                $this->info("📄 详细分析报告已导出到文件: {$filename}");
            } else {
                $this->error("❌ 报告导出失败");
            }
        }
    }
}
