<?php

declare(strict_types=1);

namespace App\Command;

use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Psr\Container\ContainerInterface;
use App\Baccarat\Model\BaccaratTerraceDeck;
use App\Baccarat\Model\BaccaratTerrace;
use App\Baccarat\Model\BaccaratSimulatedBettingLog;
use App\Baccarat\Listener\BettingBaccListener;
#[Command]
class GetTerraceDeckBettingLog extends HyperfCommand
{
    public function __construct(protected ContainerInterface $container)
    {
        parent::__construct('terrace:deck:betting:log');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('获取台桌投注日志')
        ->addOption(
            'mine-confidence',
            'c',
            \Symfony\Component\Console\Input\InputOption::VALUE_REQUIRED,
            '设置预测的最低置信度 (0-100)',
            80
        )
        ;
    }

    public function handle()
    {
        $sequences = '';

        $mineConfidence = $this->input->getOption('mine-confidence');

        $bettingLog = BaccaratSimulatedBettingLog::query()
            ->where('betting_id', BettingBaccListener::BETTING_ID)
            ->groupBy('terrace_deck_id')
            ->get(['terrace_deck_id'])
            ->map(function (BaccaratSimulatedBettingLog $item) use (&$sequences, $mineConfidence) {
                
                $sequences.= $sequence = BaccaratSimulatedBettingLog::query()
                    ->where('terrace_deck_id', $item->terrace_deck_id)
                    ->where('betting_id', BettingBaccListener::BETTING_ID)
                    ->where('credibility', '>=', $mineConfidence)
                    ->where('betting_result', '!=', 2)
                    ->get()
                    ->pluck('betting_result')
                    ->implode('')
                    ;
                // $sequence = str_replace('2', '', $bettingLog);

                

                //输出详细统计信息
                $this->line(sprintf(
                    "台桌ID: %d, 投注序列: %s",
                    $item->terrace_deck_id,
                    $sequence
                ), 'info');
        }); 
        

        //获取投注序列中输的次数
        $loseCount = substr_count($sequences, '0');
        //获取投注序列中赢的次数
        $winCount = substr_count($sequences, '1');

        //计算投注序列的胜率
        $winRate = $winCount / ($winCount + $loseCount) * 100;

        //输出详细统计信息
        $this->line(sprintf(
            "胜率: %.2f, 赢次数: %d, 输次数: %d",
            $winRate,
            $winCount,
            $loseCount
        ), 'info');
        
    }
}   
