<?php

declare(strict_types=1);

namespace App\Command;

use App\Baccarat\Service\LoggerFactory;
use App\Baccarat\Service\Output\Output;
use App\Baccarat\Service\Websocket\ConnectionPool;
use App\Baccarat\Service\Websocket\WebsocketClientFactory;
use App\Baccarat\Service\Websocket\WebSocketOrchestrator;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Hyperf\Contract\ConfigInterface;
use Hyperf\Engine\Channel;
use Hyperf\Redis\RedisFactory;
use Lysice\HyperfRedisLock\RedisLock;
use Psr\Container\ContainerInterface;
use Psr\EventDispatcher\EventDispatcherInterface;
use Swoole\Runtime;

/**
 * 重构后的WebSocket命令
 * 使用新的架构：生产者-消费者分离、统一错误处理、配置化路由
 */
#[Command]
class BaccaratWebsocketRefactored extends HyperfCommand
{
    public function __construct(
        protected ContainerInterface $container,
        protected ConfigInterface $config,
        protected RedisFactory $redisFactory,
    ) {
        parent::__construct('baccarat:websocket:refactored');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('Refactored WebSocket service with improved architecture');
    }

    public function handle()
    {
        // 验证配置
        if (!$this->config->get('websocket')) {
            throw new \Exception('WebSocket configuration is missing');
        }

        // 启用协程化
        if (Runtime::enableCoroutine() === false) {
            $this->error("Failed to enable coroutine");
            return;
        }

        $this->info("Starting refactored WebSocket service...");

        // 创建Channel（消息队列）
        $channelSize = $this->config->get('websocket.channel_size', 5000);
        $channel = new Channel($channelSize);

        // 创建WebSocket客户端工厂
        $websocketClientFactory = new WebsocketClientFactory(
            channel: $channel,
            host: $this->config->get('websocket.host'),
            token: $this->config->get('websocket.token'),
            connectionTimeout: $this->config->get('websocket.connectionTimeout')
        );

        // 创建连接池
        $connectionPool = new ConnectionPool(
            container: $this->container,
            websocketClientFactory: $websocketClientFactory,
            output: make(Output::class),
            config: $this->config->get('websocket.connectionPool'),
        );

        // 获取Redis实例
        $redis = $this->redisFactory->get('default');

        // 创建分布式锁
        $redisLock = new RedisLock($redis, "baccarat:websocket:lock:" . time(), 600);
        $reconnectLock = new RedisLock($redis, "baccarat:websocket:reconnect:lock:" . time(), 60);

        // 创建并启动协调器
        $orchestrator = new WebSocketOrchestrator(
            websocketClientFactory: $websocketClientFactory,
            connectionPool: $connectionPool,
            channel: $channel,
            output: make(Output::class),
            dispatcher: $this->container->get(EventDispatcherInterface::class),
            loggerFactory: $this->container->get(LoggerFactory::class),
            redisLock: $redisLock,
            reconnectLock: $reconnectLock,
            redisFactory: $this->redisFactory,
            consumerCount: $this->config->get('websocket.consumer_count', 10),
            producerCount: $this->config->get('websocket.producer_count', 3)
        );

        // 注册信号处理器，优雅停止
        pcntl_signal(SIGTERM, function () use ($orchestrator) {
            $this->warn("Received SIGTERM, stopping gracefully...");
            $orchestrator->stop();
        });

        pcntl_signal(SIGINT, function () use ($orchestrator) {
            $this->warn("Received SIGINT, stopping gracefully...");
            $orchestrator->stop();
        });

        // 启动服务
        try {
            $orchestrator->run();
        } catch (\Throwable $e) {
            $this->error("Service failed: " . $e->getMessage());
            $this->error($e->getTraceAsString());
            return 1;
        }

        $this->info("Service stopped successfully");
        return 0;
    }
}
