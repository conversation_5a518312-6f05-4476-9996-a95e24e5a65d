<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;
use Hyperf\DbConnection\Db;

class CreateBaccaratChartMenu extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 获取百家乐模块的父菜单ID
        $parentMenu = Db::table('system_menu')
            ->where('name', 'baccarat')
            ->where('type', 'M')
            ->first();

        if ($parentMenu) {
            // 插入大路图菜单
            Db::table('system_menu')->insert([
                'parent_id' => $parentMenu->id,
                'level' => '0,' . $parentMenu->id,
                'name' => 'baccarat:chart',
                'code' => 'baccaratChart',
                'icon' => 'icon-line-chart',
                'route' => 'baccarat/chart',
                'component' => 'baccarat/chart/index',
                'redirect' => '',
                'is_hidden' => '2',
                'type' => 'M',
                'status' => '1',
                'sort' => 100,
                'created_by' => 0,
                'updated_by' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'remark' => '百家乐大路图'
            ]);

            // 获取刚插入的菜单ID
            $chartMenu = Db::table('system_menu')
                ->where('name', 'baccarat:chart')
                ->where('parent_id', $parentMenu->id)
                ->first();

            if ($chartMenu) {
                // 插入权限按钮
                $buttons = [
                    [
                        'name' => 'baccarat:chart:rooms',
                        'code' => 'rooms',
                        'type' => 'B',
                        'remark' => '获取房间列表'
                    ],
                    [
                        'name' => 'baccarat:chart:lottery-results',
                        'code' => 'lotteryResults',
                        'type' => 'B',
                        'remark' => '获取开奖结果'
                    ],
                    [
                        'name' => 'baccarat:chart:statistics',
                        'code' => 'statistics',
                        'type' => 'B',
                        'remark' => '获取统计信息'
                    ],
                    [
                        'name' => 'baccarat:chart:export',
                        'code' => 'export',
                        'type' => 'B',
                        'remark' => '导出数据'
                    ]
                ];

                foreach ($buttons as $button) {
                    Db::table('system_menu')->insert([
                        'parent_id' => $chartMenu->id,
                        'level' => '0,' . $parentMenu->id . ',' . $chartMenu->id,
                        'name' => $button['name'],
                        'code' => $button['code'],
                        'icon' => '',
                        'route' => '',
                        'component' => '',
                        'redirect' => '',
                        'is_hidden' => '2',
                        'type' => $button['type'],
                        'status' => '1',
                        'sort' => 0,
                        'created_by' => 0,
                        'updated_by' => 0,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                        'remark' => $button['remark']
                    ]);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 删除大路图相关菜单和权限
        $chartMenu = Db::table('system_menu')
            ->where('name', 'baccarat:chart')
            ->first();

        if ($chartMenu) {
            // 删除子权限
            Db::table('system_menu')
                ->where('parent_id', $chartMenu->id)
                ->delete();

            // 删除主菜单
            Db::table('system_menu')
                ->where('id', $chartMenu->id)
                ->delete();
        }
    }
}