<?php

declare(strict_types=1);

namespace App\Baccarat\Listener;

use App\Baccarat\Event\RecvMessageEvent;
use App\Baccarat\Service\LoggerFactory;
use App\Baccarat\Service\Output\Output;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Redis\RedisFactory;
use Hyperf\Redis\RedisProxy;
use Psr\Container\ContainerInterface;
use Hyperf\Event\Contract\ListenerInterface;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\Log\LoggerInterface;

#[Listener]
class RecvMessageListener implements ListenerInterface
{
    /**
     * @var LoggerInterface
     */
    protected LoggerInterface $logger;
    protected RedisProxy $redisProxy;

    protected ?EventDispatcherInterface $eventDispatcher = null;

    public function __construct(
        protected ContainerInterface         $container,
        protected Output         $output,
        protected LoggerFactory $loggerFactory
    )
    {
        $this->redisProxy = make(RedisFactory::class)->get('default');
    }

    public function listen(): array
    {
        return [
            RecvMessageEvent::class,
        ];
    }

    public function process(object $event): void
    {
        /**
         * @var RecvMessageEvent $event
         */
        //$lotteryResult->status === 'waiting' && $lotteryResult->result && $lotteryResult->issue && $lotteryResult->isBaccarat()
        //$lotteryResult->terrace == '3001-80'

        $lotteryResult = $event->lotteryResult;

        // $this->output->info($lotteryResult);

        $this->logger = $this->loggerFactory->create($lotteryResult->terrace, 'baccarat');

        $this->logger->debug($lotteryResult);


        if ($lotteryResult->needDrawCard() && $lotteryResult->isWaiting()) {
            $this->output->info($lotteryResult);
        }
    }
}
