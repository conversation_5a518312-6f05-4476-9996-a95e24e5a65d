<?php

declare(strict_types=1);

namespace App\Baccarat\Service;

/**
 * 投注分析结果类
 * 封装投注分析的所有结果数据
 */
class BettingAnalysisResult
{
    public function __construct(
        private readonly string $originalSequence,
        private readonly string $processedSequence,
        private readonly BettingCalculator $calculator,
        private readonly float $executionTime,
        private readonly int $confidenceThreshold
    ) {}

    /**
     * 获取原始序列
     */
    public function getOriginalSequence(): string
    {
        return $this->originalSequence;
    }

    /**
     * 获取处理后的序列
     */
    public function getProcessedSequence(): string
    {
        return $this->processedSequence;
    }

    /**
     * 获取计算器实例
     */
    public function getCalculator(): BettingCalculator
    {
        return $this->calculator;
    }

    /**
     * 获取执行时间
     */
    public function getExecutionTime(): float
    {
        return $this->executionTime;
    }

    /**
     * 获取置信度阈值
     */
    public function getConfidenceThreshold(): int
    {
        return $this->confidenceThreshold;
    }

    /**
     * 获取投注结果
     */
    public function getBettingResults(): array
    {
        return $this->calculator->getBettingResults();
    }

    /**
     * 获取详细统计信息
     */
    public function getDetailedStatistics(): array
    {
        return $this->calculator->getDetailedStatistics();
    }

    /**
     * 获取基础统计信息
     */
    public function getBasicStatistics(): array
    {
        return $this->calculator->getBasicStatistics();
    }

    /**
     * 获取性能指标
     */
    public function getPerformanceMetrics(): array
    {
        return $this->calculator->getPerformanceMetrics();
    }

    /**
     * 检查是否有投注记录
     */
    public function hasBets(): bool
    {
        return count($this->calculator->getBettingResults()) > 0;
    }

    /**
     * 获取执行摘要
     */
    public function getExecutionSummary(): array
    {
        $stats = $this->getBasicStatistics();
        $performance = $this->getPerformanceMetrics();

        return [
            'sequence_length' => strlen($this->processedSequence),
            'execution_time' => round($this->executionTime, 4),
            'confidence_threshold' => $this->confidenceThreshold,
            'total_bets' => $stats['total_bets'] ?? 0,
            'win_rate' => $stats['win_rate'] ?? 0,
            'cache_hits' => $performance['cache_hits'] ?? 0,
            'api_calls' => $performance['api_calls'] ?? 0,
            'errors' => count($performance['errors'] ?? []),
        ];
    }
}
