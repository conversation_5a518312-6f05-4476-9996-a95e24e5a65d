<?php

namespace App\Baccarat\Service\Enums;

use App\Baccarat\Service\LotteryResult;

enum Bet: string
{
    case BANKER = 'BANKER';
    case PLAYER = 'PLAYER';
    case TIE = 'TIE';

    public function getOpposite(): string
    {
        return match ($this) {
            self::BANKER => LotteryResult::BANKER,
            self::PLAYER => LotteryResult::PLAYER,
            self::TIE => LotteryResult::TIE, 
        };
    }
    
}