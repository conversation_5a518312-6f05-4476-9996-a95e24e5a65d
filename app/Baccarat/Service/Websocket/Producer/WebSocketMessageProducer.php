<?php

declare(strict_types=1);

namespace App\Baccarat\Service\Websocket\Producer;

use App\Baccarat\Service\LotteryResult;
use App\Baccarat\Service\LoggerFactory;
use App\Baccarat\Service\Output\Output;
use App\Baccarat\Service\Websocket\WebsocketConnectionInterface;
use Hyperf\Coroutine\Coroutine;
use Hyperf\Engine\Contract\ChannelInterface;
use Hyperf\Redis\Redis;
use Lysice\HyperfRedisLock\RedisLock;

/**
 * WebSocket消息生产者
 * 职责：从WebSocket连接接收消息并推送到Channel
 */
class WebSocketMessageProducer
{
    private Redis $redis;

    public function __construct(
        private WebsocketConnectionInterface $connection,
        private ChannelInterface $channel,
        private Output $output,
        private LoggerFactory $loggerFactory,
        private RedisLock $redisLock,
        private RedisLock $reconnectLock,
        Redis $redis
    ) {
        $this->redis = $redis;
    }

    /**
     * 启动消息接收循环
     */
    public function start(): void
    {
        $lock = false;

        try {
            $this->output->info("Message producer started in coroutine: " . Coroutine::id());

            while (true) {
                $this->maintainConnection($lock);

                $message = $this->connection->retryRecvMessage();

                $this->processIncomingMessage($message, $lock);

                Coroutine::sleep(0.1);
            }
        } catch (\Throwable $e) {
            $this->handleError($e);
        } finally {
            if ($lock) {
                $this->redisLock->release();
            }
            $this->output->warn("Message producer stopped in coroutine: " . Coroutine::id());
        }
    }

    /**
     * 维护连接状态
     */
    private function maintainConnection(bool &$lock): void
    {
        if ($this->connection->check()) {
            $this->performReconnection($lock);
        }
    }

    /**
     * 执行重连逻辑
     */
    private function performReconnection(bool &$lock): void
    {
        $reconnectLock = null;

        try {
            $reconnectLock = $this->reconnectLock->acquire();
            if (!$reconnectLock) {
                return;
            }

            $this->output->warn("Acquired reconnect lock. Reconnecting...");

            if ($lock) {
                $this->redisLock->release();
                $lock = false;
                $this->output->warn("Released message lock for reconnection...");
            }

            $this->connection->reconnect();
        } catch (\Throwable $e) {
            $this->output->error("Reconnection failed: {$e->getMessage()}");
        } finally {
            if ($reconnectLock) {
                $this->reconnectLock->release();
                $this->output->warn("Released reconnect lock...");
            }
        }
    }

    /**
     * 处理接收到的消息
     */
    private function processIncomingMessage(mixed $message, bool &$lock): void
    {
        // 处理心跳消息
        if ($this->connection->isPing()) {
            $this->connection->push($message);
            return;
        }

        // 处理游戏更新消息
        if ($this->connection->isOnUpdateGameInfo()) {
            $this->handleGameUpdate($message, $lock);
        }
    }

    /**
     * 处理游戏更新
     */
    private function handleGameUpdate(array $message, bool &$lock): void
    {
        if (!$lock) {
            $lock = $this->acquireLock();
        }

        if ($lock) {
            $this->processGameData($message['sl'] ?? []);
        }
    }

    /**
     * 获取分布式锁
     */
    private function acquireLock(): bool
    {
        if ($this->redisLock->acquire()) {
            $this->output->info("Lock acquired in coroutine: " . Coroutine::id());
            return true;
        }
        return false;
    }

    /**
     * 处理游戏数据
     */
    private function processGameData(array $gameData): void
    {
        foreach ($gameData as $terrace => $item) {
            $lotteryResult = LotteryResult::fromArray($terrace, $item);

            // 只处理百家乐桌的消息
            if ($lotteryResult->isBaccaratTable()) {
                if ($this->shouldProcessMessage($lotteryResult)) {
                    // 推送到Channel供消费者处理
                    $this->channel->push($lotteryResult);
                }
            }
        }
    }

    /**
     * 检查消息是否应该被处理（去重逻辑）
     */
    private function shouldProcessMessage(LotteryResult $lotteryResult): bool
    {
        $messageKey = $this->generateMessageKey($lotteryResult);

        try {
            $result = $this->redis->set(
                $messageKey,
                time(),
                ['NX', 'EX' => 300] // 5分钟过期
            );

            return $result !== false;
        } catch (\Throwable $e) {
            $this->output->error("Redis deduplication failed: " . $e->getMessage());
            return true; // 降级策略：Redis异常时允许消息通过
        }
    }

    /**
     * 生成消息的唯一标识
     */
    private function generateMessageKey(LotteryResult $lotteryResult): string
    {
        return sprintf(
            "ws_msg:%s:%s",
            $lotteryResult->issue,
            $lotteryResult->status
        );
    }

    /**
     * 统一错误处理
     */
    private function handleError(\Throwable $e): void
    {
        $this->output->error("Producer error: " . $e->getMessage());
        $this->loggerFactory->get('debug', 'baccarat')->error('Producer error', [
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
}
