<?php

declare(strict_types=1);

namespace App\Baccarat\Service\Websocket\ErrorHandler;

use App\Baccarat\Service\LoggerFactory;
use App\Baccarat\Service\Output\Output;
use Hyperf\Coroutine\Coroutine;

/**
 * 统一错误处理器
 * 职责：提供一致的错误处理、日志记录和恢复策略
 */
class UnifiedErrorHandler
{
    /**
     * 错误重试配置
     */
    private array $retryConfig = [
        'max_attempts' => 3,
        'base_delay' => 1,    // 基础延迟（秒）
        'max_delay' => 60,    // 最大延迟（秒）
        'multiplier' => 2,    // 指数退避乘数
    ];

    /**
     * 错误计数器
     */
    private array $errorCounters = [];

    public function __construct(
        private Output $output,
        private LoggerFactory $loggerFactory,
        array $retryConfig = []
    ) {
        $this->retryConfig = array_merge($this->retryConfig, $retryConfig);
    }

    /**
     * 处理错误并决定是否重试
     */
    public function handle(\Throwable $e, string $context, array $contextData = []): bool
    {
        // 记录错误
        $this->logError($e, $context, $contextData);

        // 更新错误计数
        $this->incrementErrorCount($context);

        // 判断是否应该重试
        if ($this->shouldRetry($e, $context)) {
            $this->applyRetryDelay($context);
            return true;
        }

        // 执行降级策略
        $this->applyFallbackStrategy($e, $context, $contextData);

        return false;
    }

    /**
     * 记录错误日志
     */
    private function logError(\Throwable $e, string $context, array $contextData): void
    {
        $severity = $this->determineSeverity($e);

        $logData = [
            'context' => $context,
            'error_type' => get_class($e),
            'message' => $e->getMessage(),
            'code' => $e->getCode(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString(),
            'context_data' => $contextData,
            'coroutine_id' => Coroutine::id(),
        ];

        switch ($severity) {
            case 'critical':
                $this->output->error("[CRITICAL] {$context}: {$e->getMessage()}");
                $this->loggerFactory->get('error', 'baccarat')->critical($e->getMessage(), $logData);
                break;

            case 'error':
                $this->output->error("[ERROR] {$context}: {$e->getMessage()}");
                $this->loggerFactory->get('error', 'baccarat')->error($e->getMessage(), $logData);
                break;

            case 'warning':
                $this->output->warn("[WARNING] {$context}: {$e->getMessage()}");
                $this->loggerFactory->get('debug', 'baccarat')->warning($e->getMessage(), $logData);
                break;

            default:
                $this->output->info("[INFO] {$context}: {$e->getMessage()}");
                $this->loggerFactory->get('debug', 'baccarat')->info($e->getMessage(), $logData);
        }
    }

    /**
     * 判断错误严重程度
     */
    private function determineSeverity(\Throwable $e): string
    {
        // 根据异常类型判断严重程度
        if ($e instanceof \Error) {
            return 'critical';
        }

        if ($e instanceof \RuntimeException) {
            return 'error';
        }

        if ($e instanceof \LogicException) {
            return 'warning';
        }

        return 'info';
    }

    /**
     * 判断是否应该重试
     */
    private function shouldRetry(\Throwable $e, string $context): bool
    {
        // 某些错误不应该重试
        if ($this->isNonRetryableError($e)) {
            return false;
        }

        // 检查重试次数
        $attempts = $this->errorCounters[$context]['attempts'] ?? 0;
        return $attempts < $this->retryConfig['max_attempts'];
    }

    /**
     * 判断是否为不可重试的错误
     */
    private function isNonRetryableError(\Throwable $e): bool
    {
        // 逻辑错误、语法错误等不应该重试
        return $e instanceof \LogicException
            || $e instanceof \ParseError
            || $e instanceof \TypeError;
    }

    /**
     * 应用重试延迟（指数退避）
     */
    private function applyRetryDelay(string $context): void
    {
        $attempts = $this->errorCounters[$context]['attempts'] ?? 0;

        // 计算延迟时间（指数退避）
        $delay = min(
            $this->retryConfig['base_delay'] * pow($this->retryConfig['multiplier'], $attempts),
            $this->retryConfig['max_delay']
        );

        $this->output->info("Retrying {$context} after {$delay} seconds (attempt " . ($attempts + 1) . ")");

        Coroutine::sleep($delay);
    }

    /**
     * 增加错误计数
     */
    private function incrementErrorCount(string $context): void
    {
        if (!isset($this->errorCounters[$context])) {
            $this->errorCounters[$context] = [
                'attempts' => 0,
                'first_error_time' => time(),
                'last_error_time' => time(),
            ];
        }

        $this->errorCounters[$context]['attempts']++;
        $this->errorCounters[$context]['last_error_time'] = time();
    }

    /**
     * 重置错误计数
     */
    public function resetErrorCount(string $context): void
    {
        unset($this->errorCounters[$context]);
    }

    /**
     * 应用降级策略
     */
    private function applyFallbackStrategy(\Throwable $e, string $context, array $contextData): void
    {
        $this->output->warn("Applying fallback strategy for {$context}");

        // 根据不同的上下文应用不同的降级策略
        switch ($context) {
            case 'websocket_connection':
                $this->output->info("Fallback: Will retry connection in next cycle");
                break;

            case 'message_processing':
                $this->output->info("Fallback: Message discarded to prevent blocking");
                break;

            case 'event_dispatch':
                $this->output->info("Fallback: Event dispatch skipped");
                break;

            default:
                $this->output->info("Fallback: Default recovery strategy applied");
        }
    }

    /**
     * 获取错误统计信息
     */
    public function getErrorStats(): array
    {
        return [
            'counters' => $this->errorCounters,
            'total_errors' => array_sum(array_column($this->errorCounters, 'attempts')),
            'contexts_with_errors' => count($this->errorCounters),
        ];
    }

    /**
     * 清理过期的错误计数器
     */
    public function cleanupExpiredCounters(int $ttl = 3600): void
    {
        $now = time();

        foreach ($this->errorCounters as $context => $counter) {
            if ($now - $counter['last_error_time'] > $ttl) {
                unset($this->errorCounters[$context]);
            }
        }
    }
}
