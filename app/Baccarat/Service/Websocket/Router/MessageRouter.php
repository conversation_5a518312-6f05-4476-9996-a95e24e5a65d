<?php

declare(strict_types=1);

namespace App\Baccarat\Service\Websocket\Router;

use App\Baccarat\Event\BettingEvent;
use App\Baccarat\Event\RecvMessageEvent;
use App\Baccarat\Event\WaitingEvent;
use App\Baccarat\Service\LotteryResult;
use Psr\EventDispatcher\EventDispatcherInterface;

/**
 * 消息路由器
 * 职责：根据消息类型和状态路由到相应的事件
 */
class MessageRouter
{
    /**
     * 路由规则配置
     */
    private array $routes = [];

    public function __construct(
        private EventDispatcherInterface $dispatcher
    ) {
        $this->initializeRoutes();
    }

    /**
     * 初始化路由规则
     */
    private function initializeRoutes(): void
    {
        // 配置消息状态到事件的映射
        $this->routes = [
            'common' => RecvMessageEvent::class,  // 所有消息都会触发
            'betting' => BettingEvent::class,
            'waiting' => WaitingEvent::class,
            'no_draw_card' => WaitingEvent::class,
        ];
    }

    /**
     * 路由消息到对应的事件
     */
    public function route(LotteryResult $message): array
    {
        $dispatchedEvents = [];

        // 分发通用事件
        if (isset($this->routes['common'])) {
            $event = new $this->routes['common']($message);
            $this->dispatcher->dispatch($event);
            $dispatchedEvents[] = $event;
        }

        // 根据消息状态分发特定事件
        $conditions = $this->evaluateConditions($message);

        foreach ($conditions as $condition) {
            if (isset($this->routes[$condition])) {
                $eventClass = $this->routes[$condition];
                $event = new $eventClass($message);
                $this->dispatcher->dispatch($event);
                $dispatchedEvents[] = $event;
            }
        }

        return $dispatchedEvents;
    }

    /**
     * 评估消息满足的条件
     */
    private function evaluateConditions(LotteryResult $message): array
    {
        $conditions = [];

        if ($message->isBetting()) {
            $conditions[] = 'betting';
        }

        if ($message->isWaiting()) {
            $conditions[] = 'waiting';
        }

        if (!$message->needDrawCard()) {
            $conditions[] = 'no_draw_card';
        }

        return $conditions;
    }

    /**
     * 添加自定义路由规则
     */
    public function addRoute(string $condition, string $eventClass): void
    {
        $this->routes[$condition] = $eventClass;
    }

    /**
     * 移除路由规则
     */
    public function removeRoute(string $condition): void
    {
        unset($this->routes[$condition]);
    }

    /**
     * 获取所有路由规则
     */
    public function getRoutes(): array
    {
        return $this->routes;
    }
}
