<?php

declare(strict_types=1);

namespace App\Baccarat\Service\Websocket\Consumer;

use App\Baccarat\Event\BettingEvent;
use App\Baccarat\Event\RecvMessageEvent;
use App\Baccarat\Event\WaitingEvent;
use App\Baccarat\Service\LoggerFactory;
use App\Baccarat\Service\LotteryResult;
use App\Baccarat\Service\Output\Output;
use Hyperf\Coroutine\Coroutine;
use Hyperf\Engine\Contract\ChannelInterface;
use Psr\EventDispatcher\EventDispatcherInterface;

/**
 * WebSocket消息消费者
 * 职责：从Channel获取消息并分发事件
 */
class WebSocketMessageConsumer
{
    public function __construct(
        private ChannelInterface $channel,
        private EventDispatcherInterface $dispatcher,
        private Output $output,
        private LoggerFactory $loggerFactory
    ) {}

    /**
     * 启动消息消费循环
     */
    public function start(): void
    {
        try {
            $this->output->info("Message consumer started in coroutine: " . Coroutine::id());

            while (!$this->channel->isClosing()) {
                $this->consumeMessage();
            }
        } finally {
            $this->output->warn("Message consumer exiting: " . Coroutine::id());
        }
    }

    /**
     * 消费单条消息
     */
    private function consumeMessage(): void
    {
        $message = $this->channel->pop();

        if ($message === false) {
            Coroutine::sleep(0.1);
            return;
        }

        try {
            $this->processMessage($message);
        } catch (\Throwable $e) {
            $this->handleFailedMessage($message, $e);
        }
    }

    /**
     * 处理消息并分发事件
     */
    private function processMessage(LotteryResult $message): void
    {
        // 分发通用接收事件
        $this->dispatcher->dispatch(new RecvMessageEvent($message));

        // 根据消息状态分发特定事件
        if ($message->isBetting()) {
            $this->dispatcher->dispatch(new BettingEvent($message));
        }

        if ($message->isWaiting() || !$message->needDrawCard()) {
            $this->dispatcher->dispatch(new WaitingEvent($message));
        }
    }

    /**
     * 处理失败的消息
     */
    private function handleFailedMessage(mixed $message, \Throwable $e): void
    {
        $this->output->error("Message processing failed: " . $e->getMessage());

        $this->loggerFactory->get('debug', 'baccarat')->error($e->getMessage(), [
            'message' => $message instanceof LotteryResult ? $message->toArray() : $message,
            'trace' => $e->getTraceAsString()
        ]);

        // 可配置的重试策略
        // 这里暂时不重新入队，避免死循环
    }
}
