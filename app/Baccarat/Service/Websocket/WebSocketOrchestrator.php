<?php

declare(strict_types=1);

namespace App\Baccarat\Service\Websocket;

use App\Baccarat\Service\LoggerFactory;
use App\Baccarat\Service\Output\Output;
use App\Baccarat\Service\Websocket\Consumer\WebSocketMessageConsumer;
use App\Baccarat\Service\Websocket\ErrorHandler\UnifiedErrorHandler;
use App\Baccarat\Service\Websocket\Producer\WebSocketMessageProducer;
use App\Baccarat\Service\Websocket\Router\MessageRouter;
use Hyperf\Coroutine\Coroutine;
use Hyperf\Coroutine\Parallel;
use Hyperf\Engine\Contract\ChannelInterface;
use Hyperf\Redis\RedisFactory;
use Lysice\HyperfRedisLock\RedisLock;
use Psr\EventDispatcher\EventDispatcherInterface;

/**
 * WebSocket服务协调器
 * 职责：协调生产者和消费者的生命周期，管理整体服务流程
 */
class WebSocketOrchestrator
{
    private Parallel $parallel;
    private array $producers = [];
    private array $consumers = [];
    private UnifiedErrorHandler $errorHandler;

    public function __construct(
        private WebsocketClientFactory $websocketClientFactory,
        private ConnectionPool $connectionPool,
        private ChannelInterface $channel,
        private Output $output,
        private EventDispatcherInterface $dispatcher,
        private LoggerFactory $loggerFactory,
        private RedisLock $redisLock,
        private RedisLock $reconnectLock,
        private RedisFactory $redisFactory,
        private int $consumerCount = 4,
        private int $producerCount = 3
    ) {
        $this->parallel = new Parallel($this->consumerCount + $this->producerCount);
        $this->errorHandler = new UnifiedErrorHandler($output, $loggerFactory);
        $this->initialize();
    }

    /**
     * 初始化生产者和消费者
     */
    private function initialize(): void
    {
        // 创建消息路由器
        $router = new MessageRouter($this->dispatcher);

        // 初始化消费者
        for ($i = 0; $i < $this->consumerCount; $i++) {
            $this->consumers[] = new WebSocketMessageConsumer(
                $this->channel,
                $router,
                $this->output,
                $this->loggerFactory
            );
        }

        // 初始化生产者
        for ($i = 0; $i < $this->producerCount; $i++) {
            $connection = $this->connectionPool->get();
            $this->producers[] = new WebSocketMessageProducer(
                $connection,
                $this->channel,
                $this->output,
                $this->loggerFactory,
                $this->redisLock,
                $this->reconnectLock,
                $this->redisFactory->get('default')
            );
        }
    }

    /**
     * 启动服务
     */
    public function run(): void
    {
        $this->output->info("Starting WebSocket Orchestrator...");

        // 启动消费者
        $this->startConsumers();

        // 启动生产者
        $this->startProducers();

        // 启动监控协程
        $this->startMonitor();

        // 等待所有协程完成
        $this->parallel->wait();
    }

    /**
     * 启动消息消费者
     */
    private function startConsumers(): void
    {
        foreach ($this->consumers as $index => $consumer) {
            $this->parallel->add(function () use ($consumer, $index) {
                try {
                    $this->output->info("Starting consumer #{$index}");
                    $consumer->start();
                } catch (\Throwable $e) {
                    $this->errorHandler->handle($e, "consumer_{$index}", [
                        'consumer_index' => $index
                    ]);
                }
            });
        }
    }

    /**
     * 启动消息生产者
     */
    private function startProducers(): void
    {
        $initialDelay = 0;
        $intervalDelay = 10;

        foreach ($this->producers as $index => $producer) {
            $this->parallel->add(function () use ($producer, $index, &$initialDelay, $intervalDelay) {
                try {
                    // 错开启动时间，避免同时连接
                    Coroutine::sleep($initialDelay);
                    $initialDelay += $intervalDelay;

                    $this->output->info("Starting producer #{$index}");

                    // 带重试机制的生产者启动
                    while (true) {
                        try {
                            $producer->start();
                        } catch (\Throwable $e) {
                            $shouldRetry = $this->errorHandler->handle($e, "producer_{$index}", [
                                'producer_index' => $index
                            ]);

                            if (!$shouldRetry) {
                                break;
                            }
                        }
                    }
                } catch (\Throwable $e) {
                    $this->output->error("Producer #{$index} failed permanently: " . $e->getMessage());
                }
            });
        }
    }

    /**
     * 启动监控协程
     */
    private function startMonitor(): void
    {
        Coroutine::create(function () {
            while (true) {
                try {
                    // 每30秒输出一次状态
                    Coroutine::sleep(30);

                    $stats = $this->getStats();
                    $this->output->info("System Status: " . json_encode($stats));

                    // 清理过期的错误计数器
                    $this->errorHandler->cleanupExpiredCounters();
                } catch (\Throwable $e) {
                    $this->output->error("Monitor error: " . $e->getMessage());
                }
            }
        });
    }

    /**
     * 获取系统统计信息
     */
    private function getStats(): array
    {
        return [
            'channel_size' => $this->channel->getLength(),
            'channel_capacity' => $this->channel->getCapacity(),
            'consumers' => $this->consumerCount,
            'producers' => $this->producerCount,
            'error_stats' => $this->errorHandler->getErrorStats(),
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
        ];
    }

    /**
     * 优雅停止服务
     */
    public function stop(): void
    {
        $this->output->warn("Stopping WebSocket Orchestrator...");

        // 关闭Channel，触发消费者退出
        $this->channel->close();

        // 等待所有协程完成
        $this->parallel->wait();

        $this->output->info("WebSocket Orchestrator stopped");
    }
}
