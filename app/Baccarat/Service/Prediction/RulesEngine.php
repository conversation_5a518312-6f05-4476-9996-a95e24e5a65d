<?php

declare(strict_types=1);

namespace App\Baccarat\Service\Prediction;

/**
 * 百家乐规则引擎：在给定剩余牌计数的前提下，进行一次完整的无放回发牌模拟。
 * 仅关心点数（0..9），不区分花色。
 */
final class RulesEngine
{
    /** 从 counts 中按无放回抽取一张（返回点数0..9），失败返回 null */
    public function drawOne(array &$counts): ?int
    {
        $total = array_sum($counts);
        if ($total <= 0) {
            return null;
        }
        $r = mt_rand(1, $total);
        $acc = 0;
        for ($v = 0; $v <= 9; $v++) {
            $acc += $counts[$v];
            if ($r <= $acc) {
                $counts[$v]--;
                return $v;
            }
        }
        return null;
    }

    private static function sum2(int $a, int $b): int
    {
        return ($a + $b) % 10;
    }
    private static function sum3(int $a, int $b, int $c): int
    {
        return ($a + $b + $c) % 10;
    }

    /**
     * 模拟一局，返回 'B' | 'P' | 'T'。
     */
    public function simulateOnce(array $counts): ?string
    {
        // 初始牌：P1,B1,P2,B2
        $P1 = $this->drawOne($counts);
        if ($P1 === null) return null;
        $B1 = $this->drawOne($counts);
        if ($B1 === null) return null;
        $P2 = $this->drawOne($counts);
        if ($P2 === null) return null;
        $B2 = $this->drawOne($counts);
        if ($B2 === null) return null;

        $p2 = self::sum2($P1, $P2);
        $b2 = self::sum2($B1, $B2);

        // 自然
        if ($p2 >= 8 || $b2 >= 8) {
            return $p2 <=> $b2 ? ($p2 > $b2 ? 'P' : 'B') : 'T';
        }

        // 闲第三张
        $P3 = null;
        if ($p2 <= 5) {
            $P3 = $this->drawOne($counts);
            if ($P3 === null) return null;
        }

        // 庄第三张（依照标准表）
        $B3 = null;
        if ($P3 === null) {
            // 闲不补：庄 0-5 补，6-7 停
            if ($b2 <= 5) {
                $B3 = $this->drawOne($counts);
                if ($B3 === null) return null;
            }
        } else {
            // 闲补：按表
            if ($b2 <= 2) {
                $B3 = $this->drawOne($counts);
                if ($B3 === null) return null;
            } elseif ($b2 === 3 && $P3 !== 8) {
                $B3 = $this->drawOne($counts);
                if ($B3 === null) return null;
            } elseif ($b2 === 4 && in_array($P3, [2, 3, 4, 5, 6, 7], true)) {
                $B3 = $this->drawOne($counts);
                if ($B3 === null) return null;
            } elseif ($b2 === 5 && in_array($P3, [4, 5, 6, 7], true)) {
                $B3 = $this->drawOne($counts);
                if ($B3 === null) return null;
            } elseif ($b2 === 6 && in_array($P3, [6, 7], true)) {
                $B3 = $this->drawOne($counts);
                if ($B3 === null) return null;
            }
        }

        // 计算最终点数
        $pf = $P3 === null ? $p2 : self::sum3($P1, $P2, $P3);
        $bf = $B3 === null ? $b2 : self::sum3($B1, $B2, $B3);

        return $pf <=> $bf ? ($pf > $bf ? 'P' : 'B') : 'T';
    }
}
