<?php

declare(strict_types=1);

namespace App\Baccarat\Service\Prediction;

/**
 * 统一输出结果对象，便于客户端强类型消费。
 */
final class PredictionResult
{
    public function __construct(
        public float $penetration,
        public float $zMax,
        public bool $triggered,
        public ?float $pB = null,
        public ?float $pP = null,
        public ?float $pT = null,
        public ?float $pBConditional = null,
        public ?float $pPConditional = null,
        public ?float $evBanker = null,
        public ?float $evPlayer = null,
        public ?string $suggestSide = null,
        public ?float $suggestEdge = null,
    ) {}

    public function toArray(): array
    {
        return [
            'penetration' => $this->penetration,
            'zMax' => $this->zMax,
            'triggered' => $this->triggered,
            'probabilities' => $this->triggered ? [
                'banker' => $this->pB,
                'player' => $this->pP,
                'tie' => $this->pT,
            ] : null,
            'conditioned' => $this->triggered ? [
                'banker' => $this->pBConditional,
                'player' => $this->pPConditional,
            ] : null,
            'ev' => $this->triggered ? [
                'banker' => $this->evBanker,
                'player' => $this->evPlayer,
            ] : null,
            'suggest' => $this->triggered ? [
                'side' => $this->suggestSide,
                'edge' => $this->suggestEdge,
            ] : null,
        ];
    }
}
