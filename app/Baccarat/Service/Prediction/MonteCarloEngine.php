<?php

declare(strict_types=1);

namespace App\Baccarat\Service\Prediction;

final class MonteCarloEngine
{
    public function __construct(private readonly RulesEngine $rules) {}

    /**
     * 单线程蒙特卡洛模拟。
     */
    public function simulate(array $counts, int $samples = 50000): array
    {
        $wins = $this->runSimulations($counts, $samples);
        return $this->calculateResults($wins, $samples);
    }

    /**
     * 多协程并发模拟。
     */
    public function simulateConcurrent(array $counts, int $samples = 100000, int $workers = 8): array
    {
        $workers = max(1, $workers);

        if ($workers === 1 || !\extension_loaded('swoole')) {
            return $this->simulate($counts, $samples);
        }

        $wins = $this->runConcurrentSimulations($counts, $samples, $workers);
        return $this->calculateResults($wins, $samples);
    }

    private function runSimulations(array $counts, int $samples): array
    {
        $wins = ['B' => 0, 'P' => 0, 'T' => 0];

        for ($i = 0; $i < $samples; $i++) {
            $localCounts = $counts;
            $outcome = $this->rules->simulateOnce($localCounts);

            if ($outcome !== null) {
                $wins[$outcome]++;
            }
        }

        return $wins;
    }

    private function runConcurrentSimulations(array $counts, int $samples, int $workers): array
    {
        $batchSize = intdiv($samples, $workers);
        $remainder = $samples % $workers;

        $waitGroup = new \Swoole\Coroutine\WaitGroup();
        $totalWins = ['B' => 0, 'P' => 0, 'T' => 0];

        for ($i = 0; $i < $workers; $i++) {
            $currentBatchSize = $batchSize + ($i < $remainder ? 1 : 0);

            if ($currentBatchSize <= 0) {
                continue;
            }

            $waitGroup->add();
            \Swoole\Coroutine\go(function () use ($counts, $currentBatchSize, &$totalWins, $waitGroup) {
                $batchWins = $this->runSimulations($counts, $currentBatchSize);
                $totalWins['B'] += $batchWins['B'];
                $totalWins['P'] += $batchWins['P'];
                $totalWins['T'] += $batchWins['T'];
                $waitGroup->done();
            });
        }

        $waitGroup->wait();
        return $totalWins;
    }

    private function calculateResults(array $wins, int $samples): array
    {
        $pB = $wins['B'] / $samples;
        $pP = $wins['P'] / $samples;
        $pT = $wins['T'] / $samples;

        return [
            'pB' => $pB,
            'pP' => $pP,
            'pT' => $pT,
            'seB' => sqrt(max(0.0, $pB * (1 - $pB) / $samples)),
            'seP' => sqrt(max(0.0, $pP * (1 - $pP) / $samples)),
            'seT' => sqrt(max(0.0, $pT * (1 - $pT) / $samples)),
        ];
    }
}
