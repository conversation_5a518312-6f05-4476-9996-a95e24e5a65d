<?php

declare(strict_types=1);

namespace App\Baccarat\Service\Prediction;

/**
 * 维护单靴（8副牌）的剩余牌计数与穿透度。
 * 计数按百家乐点数映射：0 表示 10/J/Q/K；1-9 表示对应点数。
 */
final class ShoeState
{
    public const TOTAL_CARDS = 416; // 8 decks * 52

    /** @var int[] 剩余牌计数，长度10，下标0..9 */
    public array $counts;

    /** 已发出的总张数 */
    public int $dealtCards;

    public function __construct(?array $counts = null)
    {
        $this->counts = $counts ?? self::initialCounts();
        $this->dealtCards = self::TOTAL_CARDS - array_sum($this->counts);
    }

    /** 初始牌靴：0点128张，1~9各32张（8副牌）。 */
    public static function initialCounts(): array
    {
        $counts = array_fill(0, 10, 32); // 1..9 先置32
        $counts[0] = 128;                // 0点为128
        return $counts;
    }

    /** 当前剩余总牌数 */
    public function remainingCards(): int
    {
        return array_sum($this->counts);
    }

    /** 穿透度 = 已发牌 / 416 */
    public function penetration(): float
    {
        $remaining = $this->remainingCards();
        return max(0.0, min(1.0, (self::TOTAL_CARDS - $remaining) / self::TOTAL_CARDS));
    }

    /**
     * 从已知的牌面点数字符（A,2..9,10,J,Q,K）映射为百家乐点数 0..9。
     */
    public static function mapFaceToBaccaratPoint(string $face): int
    {
        $u = strtoupper(trim($face));
        if ($u === 'A') {
            return 1;
        }
        if ($u === 'J' || $u === 'Q' || $u === 'K') {
            return 0;
        }
        $n = (int)$u;
        if ($n >= 10) {
            return 0;
        }
        return max(0, min(9, $n));
    }

    /**
     * 根据如 "S.10" 形式的牌面字符串，扣减对应的剩余计数。
     * 传入数组应为一局内依次发出的牌；方法内部自动过滤空值。
     */
    public function consumeCards(array $cardFaces): void
    {
        foreach ($cardFaces as $card) {
            if ($card === null || $card === '') {
                continue;
            }
            $pos = strrpos($card, '.');
            $face = $pos === false ? $card : substr($card, $pos + 1);
            $point = self::mapFaceToBaccaratPoint($face);
            if ($this->counts[$point] > 0) {
                $this->counts[$point]--;
                $this->dealtCards++;
            }
        }
    }
}
