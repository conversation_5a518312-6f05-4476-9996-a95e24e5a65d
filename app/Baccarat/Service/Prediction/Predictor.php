<?php

declare(strict_types=1);

namespace App\Baccarat\Service\Prediction;

final class Predictor
{
    /** 每个点数在8副牌中的期望张数 */
    private const EXPECTED_COUNTS = [128, 32, 32, 32, 32, 32, 32, 32, 32, 32];

    /** 庄家抽水比例 */
    private const BANKER_COMMISSION = 0.95;

    /**
     * 判断是否达到预测触发条件。
     */
    public function shouldPredict(ShoeState $state, float $minPenetration, float $zThreshold): bool
    {
        return $state->penetration() >= $minPenetration
            && $this->maxZScore($state) >= $zThreshold;
    }

    /**
     * 计算最大Z分数，衡量剩余牌分布与期望的偏离程度。
     */
    public function maxZScore(ShoeState $state): float
    {
        $totalCards = ShoeState::TOTAL_CARDS;
        $dealtCards = $totalCards - $state->remainingCards();

        if ($dealtCards <= 0) {
            return 0.0;
        }

        $maxZ = 0.0;

        foreach (self::EXPECTED_COUNTS as $pointValue => $expectedCount) {
            $probability = $expectedCount / $totalCards;
            $actualDrawn = $expectedCount - $state->counts[$pointValue];
            $expectedDrawn = $dealtCards * $probability;

            $variance = $dealtCards * $probability * (1 - $probability)
                * (($totalCards - $dealtCards) / max(1, $totalCards - 1));

            if ($variance <= 0) {
                continue;
            }

            $zScore = abs(($actualDrawn - $expectedDrawn) / sqrt($variance));
            $maxZ = max($maxZ, $zScore);
        }

        return $maxZ;
    }
}
