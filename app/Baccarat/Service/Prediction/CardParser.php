<?php

declare(strict_types=1);

namespace App\Baccarat\Service\Prediction;

use App\Baccarat\Service\LotteryResult;

/**
 * 牌面解析器：统一处理各种牌面数据格式。
 */
final class CardParser
{
    /**
     * 从手牌数组解析出所有牌面（按发牌顺序：P1,B1,P2,B2,P3?,B3?）。
     *
     * @param array $rounds [{player:[..], banker:[..]}, ...]
     * @return array 扁平化的牌面数组
     */
    public function parseRounds(array $rounds): array
    {
        $cards = [];
        foreach ($rounds as $round) {
            $player = array_map(static fn($v) => 'P.' . strtoupper((string)$v), $round['player'] ?? []);
            $banker = array_map(static fn($v) => 'B.' . strtoupper((string)$v), $round['banker'] ?? []);
            $cards = array_merge($cards, $player, $banker);
        }
        return $cards;
    }

    /**
     * 从 LotteryResult 解析出本局所有牌面（按发牌顺序）。
     */
    public function parseFromLotteryResult(LotteryResult $lr): array
    {
        $player = $lr->getPlayerHand();
        $banker = $lr->getBankerHand();

        $cards = [];

        // 按标准百家乐发牌顺序：P1,B1,P2,B2,P3?,B3?
        if (!empty($player[0] ?? null)) $cards[] = $player[0];
        if (!empty($banker[0] ?? null)) $cards[] = $banker[0];
        if (!empty($player[1] ?? null)) $cards[] = $player[1];
        if (!empty($banker[1] ?? null)) $cards[] = $banker[1];
        if (!empty($player[2] ?? null)) $cards[] = $player[2];
        if (!empty($banker[2] ?? null)) $cards[] = $banker[2];

        return array_filter($cards);
    }
}
