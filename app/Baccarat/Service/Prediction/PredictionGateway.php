<?php

declare(strict_types=1);

namespace App\Baccarat\Service\Prediction;

/**
 * 统一入口：接收统一JSON结构的输入，返回统一JSON结构的输出。
 */
final class PredictionGateway
{
    public function __construct(
        private readonly MonteCarloEngine $mc,
        private readonly Predictor $predictor,
        private readonly CardParser $cardParser,
    ) {}

    // 配置属性（可通过 setter 覆盖）
    private int $samples = 50000;
    private int $workers = 8;
    private float $minPenetration = 0.25;
    private float $zThreshold = 2.0;

    public function setSamples(int $samples): self
    {
        $this->samples = max(1, $samples);
        return $this;
    }
    public function setWorkers(int $workers): self
    {
        $this->workers = max(1, $workers);
        return $this;
    }
    public function setMinPenetration(float $minPenetration): self
    {
        $this->minPenetration = max(0.0, min(1.0, $minPenetration));
        return $this;
    }
    public function setZThreshold(float $zThreshold): self
    {
        $this->zThreshold = max(0.0, $zThreshold);
        return $this;
    }

    /**
     * 仅接受 rounds 数据：[{player:[..], banker:[..]}, ...]
     */
    public function predict(array $rounds): PredictionResult
    {
        $state = new ShoeState();
        $cards = $this->cardParser->parseRounds($rounds);


        $state->consumeCards($cards);

        $penetration = $state->penetration();
        $zMax = $this->predictor->maxZScore($state);
        $triggered = $this->predictor->shouldPredict($state, $this->minPenetration, $this->zThreshold);

        if (!$triggered) {
            return new PredictionResult(
                penetration: $penetration,
                zMax: $zMax,
                triggered: false,
            );
        }

        $simulation = \extension_loaded('swoole')
            ? $this->mc->simulateConcurrent($state->counts, $this->samples, $this->workers)
            : $this->mc->simulate($state->counts, $this->samples);

        $pB = $simulation['pB'];
        $pP = $simulation['pP'];
        $pT = $simulation['pT'];

        $den = max(1e-9, 1 - $pT);
        $pB_ = $pB / $den;
        $pP_ = $pP / $den;

        $EV_B = 0.95 * $pB_ - (1 - $pB_);
        $EV_P = $pP_ - (1 - $pP_);
        $side = $EV_B >= $EV_P ? 'banker' : 'player';
        $edge = max($EV_B, $EV_P);

        return new PredictionResult(
            penetration: $penetration,
            zMax: $zMax,
            triggered: true,
            pB: $pB,
            pP: $pP,
            pT: $pT,
            pBConditional: $pB_,
            pPConditional: $pP_,
            evBanker: $EV_B,
            evPlayer: $EV_P,
            suggestSide: $side,
            suggestEdge: $edge,
        );
    }
}
