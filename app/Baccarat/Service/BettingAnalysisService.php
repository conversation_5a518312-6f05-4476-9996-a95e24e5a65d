<?php

declare(strict_types=1);

namespace App\Baccarat\Service;

use App\Baccarat\Service\Platform\Bacc\Bacc;
use Hyperf\Cache\Cache;
use Psr\Container\ContainerInterface;

/**
 * 投注分析服务类
 * 负责处理百家乐投注策略分析的核心业务逻辑
 */
class BettingAnalysisService
{
    // 配置常量
    private const MINIMUM_SEQUENCE_LENGTH = 10;
    private const DEFAULT_CONFIDENCE_THRESHOLD = 70;
    private const BANKER_SYMBOL = 'B';
    private const PLAYER_SYMBOL = 'P';
    private const TIE_SYMBOL = 'T';
    private const VALID_SEQUENCE_PATTERN = '/^[BPT]+$/i';

    // 缓存相关
    private const PREDICTIONS_CACHE_PREFIX = 'baccarat:predictions:';
    private const CACHE_TTL = 3600 * 24 * 30; // 30天缓存

    public function __construct(
        private readonly Bacc $bacc,
        private readonly Cache $cache,
        private readonly ContainerInterface $container
    ) {}

    /**
     * 分析投注序列
     *
     * @param string $sequence 原始投注序列
     * @param int $confidenceThreshold 置信度阈值
     * @param bool $enableVerbose 是否启用详细输出
     * @return BettingAnalysisResult
     */
    public function analyzeSequence(
        string $sequence,
        int $confidenceThreshold = self::DEFAULT_CONFIDENCE_THRESHOLD,
        bool $enableVerbose = false
    ): BettingAnalysisResult {

        $startTime = microtime(true);

        if ($enableVerbose) {
            echo "🚀 开始分析序列: $sequence\n";
            echo "📊 置信度阈值: $confidenceThreshold%\n";
        }

        // 1. 验证并处理序列
        $processedSequence = $this->validateAndProcessSequence($sequence);

        // 2. 获取或生成预测数据
        $predictionData = $this->getPredictionData($processedSequence, $enableVerbose);

        // 3. 基于置信度阈值计算投注决策和统计
        $calculator = $this->calculateBettingResults($processedSequence, $predictionData, $confidenceThreshold, $enableVerbose);

        // 4. 计算执行时间
        $executionTime = microtime(true) - $startTime;
        $calculator->setExecutionTime($executionTime);

        if ($enableVerbose) {
            echo "⏱️  分析完成，耗时: " . round($executionTime, 4) . " 秒\n";
        }

        // 5. 生成分析结果
        return new BettingAnalysisResult(
            originalSequence: $sequence,
            processedSequence: implode('', $processedSequence),
            calculator: $calculator,
            executionTime: $executionTime,
            confidenceThreshold: $confidenceThreshold
        );
    }

    /**
     * 获取预测数据（带缓存）
     *
     * @param array $processedSequence
     * @param bool $enableVerbose
     * @return array
     */
    private function getPredictionData(array $processedSequence, bool $enableVerbose): array
    {
        $cacheKey = $this->generatePredictionsCacheKey(implode('', $processedSequence));

        // 尝试从缓存获取预测数据
        $cachedPredictions = $this->cache->get($cacheKey);

        if ($cachedPredictions !== null) {
            return $cachedPredictions;
        }


        // 没有缓存，生成预测数据
        $predictions = [];
        $sequence = [];

        foreach ($processedSequence as $index => $currentResult) {
            $sequence[] = $currentResult;

            // 如果序列长度不足，跳过预测
            if (count($sequence) <= self::MINIMUM_SEQUENCE_LENGTH) {
                continue;
            }

            try {
                // 获取当前序列的预测
                $intSequence = $this->convertToIntegerSequence($sequence);
                $response = $this->bacc->calculate($intSequence);

                $predictions[] = [
                    'sequence_index' => $index,
                    'sequence_length' => count($sequence),
                    'sequence' => [...$sequence], // 序列快照
                    'confidence' => $response->getConfidence(),
                    'prediction' => $response->getBet() ? $response->getBet()->getOpposite() : null,
                    'raw_response' => $response,
                ];

                if ($enableVerbose) {
                    echo "  📈 序列长度 " . count($sequence) . " -> 置信度: {$response->getConfidence()}%\n";
                }
            } catch (\Exception $e) {
                if ($enableVerbose) {
                    echo "  ❌ 预测失败: {$e->getMessage()}\n";
                }
                continue;
            }
        }

        // 将预测数据存入缓存
        $this->cache->set($cacheKey, $predictions, self::CACHE_TTL);

        if ($enableVerbose) {
            echo "💾 预测数据已缓存 (" . count($predictions) . " 条预测)\n";
        }

        return $predictions;
    }

    /**
     * 基于预测数据和置信度阈值计算投注结果
     *
     * @param array $processedSequence
     * @param array $predictionData
     * @param int $confidenceThreshold
     * @param bool $enableVerbose
     * @return BettingCalculator
     */
    private function calculateBettingResults(array $processedSequence, array $predictionData, int $confidenceThreshold, bool $enableVerbose): BettingCalculator
    {
        $calculator = new BettingCalculator();
        $calculator->setConfidenceThreshold($confidenceThreshold);
        $calculator->startTiming();

        if ($enableVerbose) {
            echo "🎯 开始计算投注决策 (置信度阈值: {$confidenceThreshold}%)...\n";
        }

        $predictionIndex = 0;
        $currentBet = null;

        foreach ($processedSequence as $index => $currentResult) {
            // 检查上一局投注结果
            if ($currentBet !== null) {
                $isWin = $currentBet === $currentResult;
                $result = $isWin ? LotteryResult::BETTING_WIN : LotteryResult::BETTING_LOSE;
                $calculator->addBettingResult($result);

                // 记录详细的投注历史
                $calculator->addBetHistory([
                    'bet_on' => $currentBet,
                    'actual_result' => $currentResult,
                    'is_win' => $isWin,
                    'sequence_index' => $index,
                    'round_number' => count($calculator->getBettingResults()),
                    'timestamp' => time(),
                ]);

                if ($enableVerbose) {
                    echo "  " . ($isWin ? "✅" : "❌") . " 投注 {$currentBet}，实际 {$currentResult} -> " . ($isWin ? "胜" : "负") . "\n";
                }
            }

            // 重置投注
            $currentBet = null;

            // 添加当前结果到计算器序列
            $calculator->addToSequence($currentResult);

            // 查找对应的预测数据
            if ($predictionIndex < count($predictionData) && $predictionData[$predictionIndex]['sequence_index'] === $index) {
                $prediction = $predictionData[$predictionIndex];
                $predictionIndex++;

                // 记录预测信息（标记为缓存数据）
                $calculator->recordPrediction($prediction['sequence'], $prediction['raw_response'], true);
                $calculator->incrementCacheHits();

                // 检查置信度并设置投注
                if ($prediction['confidence'] >= $confidenceThreshold && $prediction['prediction'] !== null) {
                    $currentBet = $prediction['prediction'];

                    if ($enableVerbose) {
                        echo "  🎲 置信度 {$prediction['confidence']}% >= {$confidenceThreshold}% -> 投注 $currentBet\n";
                    }
                } else {
                    if ($enableVerbose) {
                        echo "  ⏸️  置信度 {$prediction['confidence']}% < {$confidenceThreshold}% -> 跳过投注\n";
                    }
                }
            }
        }

        if ($enableVerbose) {
            $stats = $calculator->getBasicStatistics();
            echo "📊 投注统计: 总投注 {$stats['total_bets']} 次, 胜率 {$stats['win_rate']}%\n";
        }

        return $calculator;
    }

    /**
     * 生成预测数据缓存键（仅基于序列）
     *
     * @param string $sequence 完整序列
     * @return string
     */
    private function generatePredictionsCacheKey(string $sequence): string
    {
        // 标准化序列（转大写，移除空格）
        $normalizedSequence = strtoupper(trim($sequence));
        return self::PREDICTIONS_CACHE_PREFIX . md5($normalizedSequence);
    }

    /**
     * 将序列转换为整数数组
     * 
     * @param array $sequence
     * @return array
     */
    private function convertToIntegerSequence(array $sequence): array
    {
        $sequenceString = implode('', $sequence);
        $convertedString = str_replace(
            [self::BANKER_SYMBOL, self::PLAYER_SYMBOL],
            ['1', '0'],
            $sequenceString
        );
        return array_map('intval', str_split($convertedString));
    }

    /**
     * 验证并处理序列
     *
     * @param string $sequence
     * @return array
     * @throws \InvalidArgumentException
     */
    private function validateAndProcessSequence(string $sequence): array
    {
        if (empty($sequence)) {
            throw new \InvalidArgumentException('序列不能为空');
        }

        $sequence = strtoupper(trim($sequence));

        if (!preg_match(self::VALID_SEQUENCE_PATTERN, $sequence)) {
            throw new \InvalidArgumentException('序列格式无效，只允许 B、P、T 字符');
        }

        // 移除和局(T)并转换为数组
        $cleanedSequence = str_replace(self::TIE_SYMBOL, '', $sequence);
        return str_split($cleanedSequence);
    }

    /**
     * 导出分析报告
     *
     * @param BettingAnalysisResult $result
     * @param string $filename
     * @return bool
     */
    public function exportReport(BettingAnalysisResult $result, string $filename): bool
    {
        try {
            $report = [
                'meta' => [
                    'generated_at' => date('Y-m-d H:i:s'),
                    'execution_time' => $result->getExecutionTime(),
                    'confidence_threshold' => $result->getConfidenceThreshold(),
                    'cache_info' => [
                        'cache_hit_rate' => $result->getPerformanceMetrics()['cache_hit_rate'],
                        'api_calls' => $result->getPerformanceMetrics()['api_calls'],
                        'cache_hits' => $result->getPerformanceMetrics()['cache_hits'],
                    ],
                ],
                'sequence' => [
                    'original' => $result->getOriginalSequence(),
                    'processed' => $result->getProcessedSequence(),
                ],
                'betting_results' => $result->getCalculator()->getBettingResults(),
                'detailed_statistics' => $result->getCalculator()->getDetailedStatistics(),
                'performance_metrics' => $result->getCalculator()->getPerformanceMetrics(),
            ];

            file_put_contents($filename, json_encode($report, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 清除特定序列的缓存
     *
     * @param string $sequence
     * @param int $confidenceThreshold (此参数现在不再使用，保留为了向后兼容)
     * @return bool
     */
    public function clearSequenceCache(string $sequence, int $confidenceThreshold): bool
    {
        try {
            // 移除和局并标准化序列
            $cleanedSequence = str_replace(self::TIE_SYMBOL, '', strtoupper(trim($sequence)));
            $cacheKey = $this->generatePredictionsCacheKey($cleanedSequence);
            return $this->cache->delete($cacheKey);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 清除所有预测缓存
     *
     * @return bool
     */
    public function clearAllPredictionsCache(): bool
    {
        try {
            // 这里需要根据具体的缓存实现来清除以特定前缀开始的键
            // 不同的缓存驱动实现方式可能不同
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
