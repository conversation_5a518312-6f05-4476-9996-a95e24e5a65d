<?php

declare(strict_types=1);

namespace App\Baccarat\Service;

/**
 * 投注分析输出服务类
 * 负责格式化和输出分析结果
 */
class BettingAnalysisOutputService
{
    /**
     * 输出基础投注结果
     */
    public function outputBasicResults(BettingAnalysisResult $result): array
    {
        $stats = $result->getBasicStatistics();
        $performance = $result->getPerformanceMetrics();

        $output = [];
        $output[] = "=== 投注结果统计 ===";
        $output[] = "投注序列: " . $result->getProcessedSequence();
        $output[] = "投注结果: " . json_encode($result->getBettingResults(), JSON_UNESCAPED_UNICODE);

        if ($stats['total_bets'] > 0) {
            $output[] = sprintf(
                "基础统计: 总投注 %d 次, 胜 %d 次, 负 %d 次, 胜率 %.2f%%",
                $stats['total_bets'],
                $stats['wins'],
                $stats['losses'],
                $stats['win_rate']
            );
        } else {
            $output[] = "没有进行任何投注";
        }

        // 性能信息
        $output[] = sprintf(
            "执行信息: 耗时 %.4f 秒, 置信度阈值 %d%%, 缓存命中率 %.2f%% (API调用: %d次, 缓存命中: %d次)",
            $result->getExecutionTime(),
            $result->getConfidenceThreshold(),
            $performance['cache_hit_rate'],
            $performance['api_calls'],
            $performance['cache_hits']
        );

        return $output;
    }

    /**
     * 输出详细分析报告
     */
    public function outputDetailedAnalysis(BettingAnalysisResult $result): array
    {
        $stats = $result->getDetailedStatistics();
        $performance = $result->getPerformanceMetrics();
        $output = [];

        $output[] = "\n=== 详细分析报告 ===";
        $output[] = sprintf("预测的最低置信度: %d%%", $result->getConfidenceThreshold());

        // 执行性能分析
        $output[] = "\n⚡ 执行性能分析:";
        $output[] = sprintf("  - 总执行时间: %.4f 秒", $performance['execution_time']);
        $output[] = sprintf("  - API调用次数: %d 次", $performance['api_calls']);
        $output[] = sprintf("  - 缓存命中次数: %d 次", $performance['cache_hits']);
        $output[] = sprintf("  - 缓存命中率: %.2f%%", $performance['cache_hit_rate']);
        $output[] = sprintf("  - 错误次数: %d 次", count($performance['errors']));

        // 连胜连败分析
        if (isset($stats['streaks'])) {
            $streaks = $stats['streaks'];
            $output[] = "\n🔥 连胜连败分析:";
            $output[] = sprintf(
                "  - 当前连续: %s %d 次",
                $streaks['current_streak_type'] === 'win' ? '连胜' : ($streaks['current_streak_type'] === 'lose' ? '连败' : '无'),
                $streaks['current_streak']
            );
            $output[] = sprintf("  - 最长连胜: %d 次", $streaks['max_win_streak']);
            $output[] = sprintf("  - 最长连败: %d 次", $streaks['max_lose_streak']);
            $output[] = sprintf("  - 总连胜段数: %d 段", $streaks['total_win_streaks']);
            $output[] = sprintf("  - 总连败段数: %d 段", $streaks['total_lose_streaks']);
        }

        // 投注分布分析
        if (isset($stats['bet_distribution'])) {
            $dist = $stats['bet_distribution'];
            $output[] = "\n📊 投注分布分析:";
            $output[] = sprintf("  - 庄家投注: %d 次 (胜率: %.2f%%)", $dist['banker_bets'], $dist['banker_win_rate']);
            $output[] = sprintf("  - 闲家投注: %d 次 (胜率: %.2f%%)", $dist['player_bets'], $dist['player_win_rate']);
            $output[] = sprintf("  - 投注偏好: %s", $dist['bet_preference']);
        }

        // 性能指标分析
        if (isset($stats['performance'])) {
            $perf = $stats['performance'];
            $output[] = "\n📈 性能指标分析:";
            $output[] = sprintf("  - 整体胜率: %.2f%%", $perf['overall_win_rate']);
            $output[] = sprintf("  - 近期胜率: %.2f%%", $perf['recent_win_rate']);
            $output[] = sprintf("  - 稳定性指标: %s", $perf['stability_indicator']);
            $output[] = sprintf("  - 风险级别: %s", $perf['risk_level']);
            $output[] = sprintf("  - 表现趋势: %s", $perf['performance_trend']);
        }

        // 趋势分析
        if (isset($stats['trends']) && !isset($stats['trends']['insufficient_data'])) {
            $trends = $stats['trends'];
            $output[] = "\n📉 趋势分析:";
            $output[] = sprintf("  - 近期趋势: %s", $trends['recent_trend']);
            $output[] = sprintf("  - 庄家频率: %.2f%%", $trends['banker_frequency']);
            $output[] = sprintf("  - 闲家频率: %.2f%%", $trends['player_frequency']);
            $output[] = sprintf("  - 波动性: %s", $trends['volatility']);
        }

        // 预测准确率
        if (isset($stats['prediction_accuracy']) && !isset($stats['prediction_accuracy']['no_predictions'])) {
            $pred = $stats['prediction_accuracy'];
            $output[] = "\n🎯 预测准确率:";
            $output[] = sprintf("  - 总预测次数: %d 次", $pred['total_predictions']);
            $output[] = sprintf("  - 高置信度预测: %d 次 (%.2f%%)", $pred['high_confidence_predictions'], $pred['high_confidence_rate']);
            $output[] = sprintf("  - 平均置信度: %.2f%%", $pred['average_confidence']);
            $output[] = sprintf("  - 缓存使用率: %.2f%%", $pred['cache_usage_rate']);
        }

        return $output;
    }

    /**
     * 输出建议和改进策略
     */
    public function outputRecommendations(BettingAnalysisResult $result): array
    {
        $stats = $result->getDetailedStatistics();
        $output = [];

        $output[] = "\n=== 投注建议 ===";

        $recommendations = [];

        // 基于胜率的建议
        if ($stats['win_rate'] >= 60) {
            $recommendations[] = "✅ 投注策略表现优秀，建议继续当前策略";
        } elseif ($stats['win_rate'] >= 45) {
            $recommendations[] = "⚠️  投注策略表现一般，建议调整投注逻辑";
        } else {
            $recommendations[] = "❌ 投注策略表现不佳，建议重新评估策略";
        }

        // 基于连败的建议
        if (isset($stats['streaks']['max_lose_streak']) && $stats['streaks']['max_lose_streak'] >= 5) {
            $recommendations[] = "🛑 检测到长期连败，建议设置止损点";
        }

        // 基于风险级别的建议
        if (isset($stats['performance']['risk_level'])) {
            switch ($stats['performance']['risk_level']) {
                case '高风险':
                    $recommendations[] = "⚠️  当前风险级别较高，建议降低投注金额";
                    break;
                case '低风险':
                    $recommendations[] = "✅ 当前风险控制良好，可适当提高投注";
                    break;
            }
        }

        // 基于趋势的建议
        if (isset($stats['performance']['performance_trend'])) {
            if ($stats['performance']['performance_trend'] === '上升') {
                $recommendations[] = "📈 表现趋势向上，策略效果正在改善";
            } else {
                $recommendations[] = "📉 表现趋势向下，需要关注策略调整";
            }
        }

        // 基于投注分布的建议
        if (isset($stats['bet_distribution'])) {
            $dist = $stats['bet_distribution'];
            if ($dist['banker_win_rate'] > $dist['player_win_rate'] + 10) {
                $recommendations[] = "🏦 庄家投注胜率更高，建议偏向庄家投注";
            } elseif ($dist['player_win_rate'] > $dist['banker_win_rate'] + 10) {
                $recommendations[] = "👤 闲家投注胜率更高，建议偏向闲家投注";
            }
        }

        // 性能优化建议
        $performance = $result->getPerformanceMetrics();
        if ($performance['cache_hit_rate'] < 50) {
            $recommendations[] = "🚀 缓存命中率较低，可能需要增加缓存时间";
        } elseif ($performance['cache_hit_rate'] > 80) {
            $recommendations[] = "💾 缓存效果良好，显著提升了执行效率";
        }

        foreach ($recommendations as $recommendation) {
            $output[] = $recommendation;
        }

        if (empty($recommendations)) {
            $output[] = "📊 数据不足，无法提供具体建议";
        }

        return $output;
    }

    /**
     * 格式化输出所有信息
     */
    public function formatOutput(BettingAnalysisResult $result): string
    {
        $output = [];

        $output = array_merge($output, $this->outputBasicResults($result));
        $output = array_merge($output, $this->outputDetailedAnalysis($result));
        $output = array_merge($output, $this->outputRecommendations($result));

        return implode("\n", $output);
    }

    /**
     * 输出到控制台
     */
    public function outputToConsole(BettingAnalysisResult $result): void
    {
        echo $this->formatOutput($result) . "\n";
    }
}
