<?php

declare(strict_types=1);

namespace App\Baccarat\Service;

/**
 * 投注计算器类
 * 负责处理百家乐投注序列的计算和结果统计
 */
class BettingCalculator
{
    private const BANKER_SYMBOL = 'B';
    private const PLAYER_SYMBOL = 'P';

    private array $sequence = [];
    private ?string $currentBet = null;
    private array $bettingResults = [];
    private array $betHistory = []; // 投注历史记录
    private array $predictionHistory = []; // 预测历史记录

    // 性能和配置追踪
    private array $errors = [];
    private int $cacheHits = 0;
    private int $apiCalls = 0;
    private float $startTime = 0;
    private float $executionTime = 0;
    private int $confidenceThreshold = 70;

    /**
     * 开始计时
     */
    public function startTiming(): void
    {
        $this->startTime = microtime(true);
    }

    /**
     * 设置执行时间
     */
    public function setExecutionTime(float $time): void
    {
        $this->executionTime = $time;
    }

    /**
     * 设置置信度阈值
     */
    public function setConfidenceThreshold(int $threshold): void
    {
        $this->confidenceThreshold = $threshold;
    }

    /**
     * 获取置信度阈值
     */
    public function getConfidenceThreshold(): int
    {
        return $this->confidenceThreshold;
    }

    /**
     * 增加缓存命中计数
     */
    public function incrementCacheHits(): void
    {
        $this->cacheHits++;
    }

    /**
     * 增加API调用计数
     */
    public function incrementApiCalls(): void
    {
        $this->apiCalls++;
    }

    /**
     * 记录错误
     */
    public function recordError(string $error): void
    {
        $this->errors[] = [
            'message' => $error,
            'timestamp' => time(),
        ];
    }

    /**
     * 检查上一局投注结果
     */
    public function checkPreviousBetResult(string $currentResult): void
    {
        if ($this->currentBet === null) {
            return;
        }

        $isWin = $this->currentBet === $currentResult;
        $result = $isWin ? LotteryResult::BETTING_WIN : LotteryResult::BETTING_LOSE;

        $this->bettingResults[] = $result;

        // 记录详细的投注历史
        $this->betHistory[] = [
            'bet_on' => $this->currentBet,
            'actual_result' => $currentResult,
            'is_win' => $isWin,
            'sequence_length' => count($this->sequence),
            'round_number' => count($this->betHistory) + 1,
            'timestamp' => time(),
        ];
    }

    /**
     * 重置投注
     */
    public function resetBet(): void
    {
        $this->currentBet = null;
    }

    /**
     * 设置投注
     */
    public function setBet(string $bet): void
    {
        $this->currentBet = $bet;
    }

    /**
     * 记录预测信息（增强版）
     */
    public function recordPrediction(array $sequence, $response, bool $fromCache = false): void
    {
        $this->predictionHistory[] = [
            'sequence' => $sequence,
            'confidence' => $response->getConfidence(),
            'prediction' => $response->getBet() ? $response->getBet()->getOpposite() : null,
            'from_cache' => $fromCache,
            'timestamp' => time(),
        ];

        // 更新计数器
        if ($fromCache) {
            $this->cacheHits++;
        } else {
            $this->apiCalls++;
        }
    }

    /**
     * 添加结果到序列
     */
    public function addToSequence(string $result): void
    {
        $this->sequence[] = $result;
    }

    /**
     * 检查是否达到最小序列长度
     */
    public function hasMinimumSequenceLength(int $minimumLength): bool
    {
        return count($this->sequence) > $minimumLength;
    }

    /**
     * 获取当前序列
     */
    public function getSequence(): array
    {
        return $this->sequence;
    }

    /**
     * 获取整数序列（用于计算）
     * 将 B 转换为 1，P 转换为 0
     */
    public function getIntegerSequence(): array
    {
        $sequenceString = implode('', $this->sequence);
        $convertedString = str_replace(
            [self::BANKER_SYMBOL, self::PLAYER_SYMBOL],
            ['1', '0'],
            $sequenceString
        );

        return array_map('intval', str_split($convertedString));
    }

    /**
     * 获取投注结果
     */
    public function getBettingResults(): array
    {
        return $this->bettingResults;
    }

    /**
     * 获取基础统计信息
     */
    public function getBasicStatistics(): array
    {
        $winCount = count(array_filter($this->bettingResults, fn($result) => $result === LotteryResult::BETTING_WIN));
        $loseCount = count(array_filter($this->bettingResults, fn($result) => $result === LotteryResult::BETTING_LOSE));
        $totalBets = count($this->bettingResults);

        return [
            'total_bets' => $totalBets,
            'wins' => $winCount,
            'losses' => $loseCount,
            'win_rate' => $totalBets > 0 ? round(($winCount / $totalBets) * 100, 2) : 0,
        ];
    }

    /**
     * 获取性能指标
     */
    public function getPerformanceMetrics(): array
    {
        return [
            'execution_time' => $this->executionTime,
            'cache_hits' => $this->cacheHits,
            'api_calls' => $this->apiCalls,
            'cache_hit_rate' => $this->cacheHits + $this->apiCalls > 0
                ? round(($this->cacheHits / ($this->cacheHits + $this->apiCalls)) * 100, 2)
                : 0,
            'total_predictions' => count($this->predictionHistory),
            'errors' => $this->errors,
            'confidence_threshold' => $this->confidenceThreshold,
        ];
    }

    /**
     * 获取详细统计信息
     */
    public function getDetailedStatistics(): array
    {
        $basic = $this->getBasicStatistics();
        $streaks = $this->calculateStreaks();
        $distribution = $this->calculateBetDistribution();
        $performance = $this->calculatePerformanceMetrics();
        $trends = $this->calculateTrends();

        return array_merge($basic, [
            'streaks' => $streaks,
            'bet_distribution' => $distribution,
            'performance' => $performance,
            'trends' => $trends,
            'prediction_accuracy' => $this->calculatePredictionAccuracy(),
            'execution_metrics' => $this->getPerformanceMetrics(),
        ]);
    }

    /**
     * 计算连胜连败统计
     */
    private function calculateStreaks(): array
    {
        if (empty($this->bettingResults)) {
            return [
                'current_streak' => 0,
                'current_streak_type' => null,
                'max_win_streak' => 0,
                'max_lose_streak' => 0,
                'total_win_streaks' => 0,
                'total_lose_streaks' => 0,
            ];
        }

        $currentStreak = 0;
        $currentStreakType = null;
        $maxWinStreak = 0;
        $maxLoseStreak = 0;
        $winStreaks = 0;
        $loseStreaks = 0;
        $tempWinStreak = 0;
        $tempLoseStreak = 0;

        foreach ($this->bettingResults as $result) {
            if ($result === LotteryResult::BETTING_WIN) {
                if ($currentStreakType === 'win') {
                    $currentStreak++;
                } else {
                    if ($tempLoseStreak > 0) {
                        $loseStreaks++;
                        $maxLoseStreak = max($maxLoseStreak, $tempLoseStreak);
                        $tempLoseStreak = 0;
                    }
                    $currentStreak = 1;
                    $currentStreakType = 'win';
                }
                $tempWinStreak = $currentStreak;
            } else {
                if ($currentStreakType === 'lose') {
                    $currentStreak++;
                } else {
                    if ($tempWinStreak > 0) {
                        $winStreaks++;
                        $maxWinStreak = max($maxWinStreak, $tempWinStreak);
                        $tempWinStreak = 0;
                    }
                    $currentStreak = 1;
                    $currentStreakType = 'lose';
                }
                $tempLoseStreak = $currentStreak;
            }
        }

        // 处理最后的连胜/连败
        if ($currentStreakType === 'win' && $tempWinStreak > 0) {
            $winStreaks++;
            $maxWinStreak = max($maxWinStreak, $tempWinStreak);
        } elseif ($currentStreakType === 'lose' && $tempLoseStreak > 0) {
            $loseStreaks++;
            $maxLoseStreak = max($maxLoseStreak, $tempLoseStreak);
        }

        return [
            'current_streak' => $currentStreak,
            'current_streak_type' => $currentStreakType,
            'max_win_streak' => $maxWinStreak,
            'max_lose_streak' => $maxLoseStreak,
            'total_win_streaks' => $winStreaks,
            'total_lose_streaks' => $loseStreaks,
        ];
    }

    /**
     * 计算投注分布
     */
    private function calculateBetDistribution(): array
    {
        $bankerBets = 0;
        $playerBets = 0;
        $bankerWins = 0;
        $playerWins = 0;

        foreach ($this->betHistory as $bet) {
            if ($bet['bet_on'] === self::BANKER_SYMBOL) {
                $bankerBets++;
                if ($bet['is_win']) {
                    $bankerWins++;
                }
            } elseif ($bet['bet_on'] === self::PLAYER_SYMBOL) {
                $playerBets++;
                if ($bet['is_win']) {
                    $playerWins++;
                }
            }
        }

        return [
            'banker_bets' => $bankerBets,
            'player_bets' => $playerBets,
            'banker_wins' => $bankerWins,
            'player_wins' => $playerWins,
            'banker_win_rate' => $bankerBets > 0 ? round(($bankerWins / $bankerBets) * 100, 2) : 0,
            'player_win_rate' => $playerBets > 0 ? round(($playerWins / $playerBets) * 100, 2) : 0,
            'bet_preference' => $bankerBets > $playerBets ? '偏好投注庄家' : ($playerBets > $bankerBets ? '偏好投注闲家' : '投注均衡'),
        ];
    }

    /**
     * 计算性能指标
     */
    private function calculatePerformanceMetrics(): array
    {
        $totalBets = count($this->bettingResults);
        if ($totalBets === 0) {
            return [];
        }

        $wins = count(array_filter($this->bettingResults, fn($result) => $result === LotteryResult::BETTING_WIN));

        // 计算稳定性指标 (连续投注的标准差)
        $recentResults = array_slice($this->bettingResults, -10); // 最近10次投注
        $recentWinRate = count($recentResults) > 0
            ? count(array_filter($recentResults, fn($result) => $result === LotteryResult::BETTING_WIN)) / count($recentResults) * 100
            : 0;

        return [
            'overall_win_rate' => round(($wins / $totalBets) * 100, 2),
            'recent_win_rate' => round($recentWinRate, 2),
            'stability_indicator' => $this->calculateStabilityIndicator(),
            'risk_level' => $this->calculateRiskLevel(),
            'performance_trend' => $recentWinRate > ($wins / $totalBets * 100) ? '上升' : '下降',
        ];
    }

    /**
     * 计算趋势分析
     */
    private function calculateTrends(): array
    {
        if (count($this->sequence) < 5) {
            return ['insufficient_data' => true];
        }

        $recent = array_slice($this->sequence, -5);
        $bankerCount = count(array_filter($recent, fn($result) => $result === self::BANKER_SYMBOL));
        $playerCount = count(array_filter($recent, fn($result) => $result === self::PLAYER_SYMBOL));

        return [
            'recent_trend' => $bankerCount > $playerCount ? '庄家优势' : ($playerCount > $bankerCount ? '闲家优势' : '平衡'),
            'banker_frequency' => round(($bankerCount / count($recent)) * 100, 2),
            'player_frequency' => round(($playerCount / count($recent)) * 100, 2),
            'volatility' => $this->calculateVolatility($recent),
        ];
    }

    /**
     * 计算预测准确率
     */
    private function calculatePredictionAccuracy(): array
    {
        if (empty($this->predictionHistory)) {
            return ['no_predictions' => true];
        }

        $totalPredictions = count($this->predictionHistory);
        $highConfidencePredictions = count(array_filter($this->predictionHistory, fn($pred) => $pred['confidence'] > 70));
        $avgConfidence = array_sum(array_column($this->predictionHistory, 'confidence')) / $totalPredictions;
        $cacheUsage = count(array_filter($this->predictionHistory, fn($pred) => $pred['from_cache'] ?? false));

        return [
            'total_predictions' => $totalPredictions,
            'high_confidence_predictions' => $highConfidencePredictions,
            'high_confidence_rate' => round(($highConfidencePredictions / $totalPredictions) * 100, 2),
            'average_confidence' => round($avgConfidence, 2),
            'cache_usage_rate' => round(($cacheUsage / $totalPredictions) * 100, 2),
        ];
    }

    /**
     * 计算稳定性指标
     */
    private function calculateStabilityIndicator(): string
    {
        $streaks = $this->calculateStreaks();
        $maxStreak = max($streaks['max_win_streak'], $streaks['max_lose_streak']);

        if ($maxStreak <= 3) {
            return '稳定';
        } elseif ($maxStreak <= 6) {
            return '中等';
        } else {
            return '波动较大';
        }
    }

    /**
     * 计算风险级别
     */
    private function calculateRiskLevel(): string
    {
        $streaks = $this->calculateStreaks();
        $winRate = $this->getBasicStatistics()['win_rate'];

        if ($winRate >= 60 && $streaks['max_lose_streak'] <= 3) {
            return '低风险';
        } elseif ($winRate >= 45 && $streaks['max_lose_streak'] <= 5) {
            return '中等风险';
        } else {
            return '高风险';
        }
    }

    /**
     * 计算波动性
     */
    private function calculateVolatility(array $sequence): string
    {
        if (count($sequence) <= 1) {
            return '无法计算';
        }

        $changes = 0;
        for ($i = 1; $i < count($sequence); $i++) {
            if ($sequence[$i] !== $sequence[$i - 1]) {
                $changes++;
            }
        }

        $volatilityRate = $changes / (count($sequence) - 1);

        if ($volatilityRate <= 0.3) {
            return '低波动';
        } elseif ($volatilityRate <= 0.7) {
            return '中等波动';
        } else {
            return '高波动';
        }
    }

    /**
     * 重置计算器状态
     */
    public function reset(): void
    {
        $this->sequence = [];
        $this->currentBet = null;
        $this->bettingResults = [];
        $this->betHistory = [];
        $this->predictionHistory = [];
        $this->errors = [];
        $this->cacheHits = 0;
        $this->apiCalls = 0;
        $this->startTime = 0;
        $this->executionTime = 0;
    }

    /**
     * 获取投注历史
     */
    public function getBetHistory(): array
    {
        return $this->betHistory;
    }

    /**
     * 获取预测历史
     */
    public function getPredictionHistory(): array
    {
        return $this->predictionHistory;
    }

    /**
     * 获取错误列表
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    // 保持向后兼容
    public function getStatistics(): array
    {
        return $this->getBasicStatistics();
    }

    /**
     * 添加投注结果
     */
    public function addBettingResult($result): void
    {
        $this->bettingResults[] = $result;
    }

    /**
     * 添加投注历史记录
     */
    public function addBetHistory(array $betRecord): void
    {
        $this->betHistory[] = $betRecord;
    }
}
