<?php
declare(strict_types=1);

namespace App\Baccarat\Controller;

use App\Baccarat\Service\BaccaratLotteryLogService;
use App\Baccarat\Service\BaccaratTerraceService;
use App\Baccarat\Service\BaccaratTerraceDeckService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Mine\Annotation\Auth;
use Mine\Annotation\Permission;
use Mine\MineController;
use Psr\Http\Message\ResponseInterface;
use Mine\Middlewares\CheckModuleMiddleware;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * 百家乐图表控制器
 * Class BaccaratChartController
 */
#[Controller(prefix: "baccarat/chart"), Auth]
#[Middleware(middleware: CheckModuleMiddleware::class)]
class BaccaratChartController extends MineController
{
    #[Inject]
    protected BaccaratLotteryLogService $lotteryLogService;

    #[Inject]
    protected BaccaratTerraceService $terraceService;

    #[Inject]
    protected BaccaratTerraceDeckService $terraceDeckService;

    /**
     * 获取可用的房间列表
     * @return ResponseInterface
     */
    #[GetMapping("rooms"), Permission("baccarat:chart, baccarat:chart:rooms")]
    public function getRooms(): ResponseInterface
    {
        $rooms = $this->terraceService->mapper->getModel()
            ->select(['id', 'code', 'title'])
            ->get();
        
        return $this->success($rooms);
    }

    /**
     * 获取指定日期和房间的开奖结果
     * @return ResponseInterface
     */
    #[GetMapping("lottery-results"), Permission("baccarat:chart, baccarat:chart:lottery-results")]
    public function getLotteryResults(): ResponseInterface
    {
        $params = $this->request->all();
        
        // 验证参数
        if (empty($params['date']) || empty($params['terrace_id'])) {
            return $this->error('日期和房间参数必填');
        }

        $date = $params['date'];
        $terraceId = (int)$params['terrace_id'];

        // 获取指定日期和房间的所有牌靴
        $decks = $this->terraceDeckService->mapper->getModel()
            ->where('terrace_id', $terraceId)
            ->whereDate('created_at', $date)
            ->orderBy('deck_number', 'asc')
            ->get(['id', 'deck_number', 'created_at']);

        if ($decks->isEmpty()) {
            return $this->success([
                'decks' => [],
                'results' => [],
                'chart_data' => []
            ]);
        }

        // 获取所有牌靴的开奖记录
        $deckIds = $decks->pluck('id')->toArray();
        $lotteryLogs = $this->lotteryLogService->mapper->getModel()
            ->whereIn('terrace_deck_id', $deckIds)
            ->whereNotNull('transformationResult')
            ->orderBy('issue', 'asc')
            ->get(['id', 'terrace_deck_id', 'issue', 'result', 'transformationResult', 'created_at']);

        // 组装大路图数据
        $chartData = $this->buildBigRoadChartData($lotteryLogs);

        return $this->success([
            'decks' => $decks,
            'results' => $lotteryLogs,
            'chart_data' => $chartData
        ]);
    }

    /**
     * 构建大路图数据
     * @param $lotteryLogs
     * @return array
     */
    private function buildBigRoadChartData($lotteryLogs): array
    {
        $chartData = [];
        $currentColumn = 0;
        $currentRow = 0;
        $lastResult = null;
        $maxRow = 6; // 大路图最大行数

        foreach ($lotteryLogs as $log) {
            $result = $log->transformationResult;
            
            // 转换结果为B(庄)、P(闲)、T(和)
            $displayResult = match($result) {
                '1' => 'B', // 庄
                '2' => 'P', // 闲
                '3' => 'T', // 和
                default => $result
            };

            // 如果是第一个结果或结果与上一个不同
            if ($lastResult === null || $displayResult !== $lastResult) {
                // 如果不是第一个结果，移到下一列
                if ($lastResult !== null) {
                    $currentColumn++;
                    $currentRow = 0;
                }
            } else {
                // 相同结果，向下移动
                $currentRow++;
                // 如果超过最大行数，转向右边
                if ($currentRow >= $maxRow) {
                    $currentColumn++;
                    $currentRow = $maxRow - 1;
                }
            }

            $chartData[] = [
                'column' => $currentColumn,
                'row' => $currentRow,
                'result' => $displayResult,
                'issue' => $log->issue,
                'deck_id' => $log->terrace_deck_id,
                'created_at' => $log->created_at->toDateTimeString()
            ];

            $lastResult = $displayResult;
        }

        return $chartData;
    }

    /**
     * 获取日期范围内的统计数据
     * @return ResponseInterface
     */
    #[GetMapping("statistics"), Permission("baccarat:chart, baccarat:chart:statistics")]
    public function getStatistics(): ResponseInterface
    {
        $params = $this->request->all();
        
        if (empty($params['date']) || empty($params['terrace_id'])) {
            return $this->error('日期和房间参数必填');
        }

        $date = $params['date'];
        $terraceId = (int)$params['terrace_id'];

        // 获取指定日期和房间的所有牌靴
        $decks = $this->terraceDeckService->mapper->getModel()
            ->where('terrace_id', $terraceId)
            ->whereDate('created_at', $date)
            ->get(['id']);

        if ($decks->isEmpty()) {
            return $this->success([
                'total' => 0,
                'banker_wins' => 0,
                'player_wins' => 0,
                'ties' => 0,
                'banker_percentage' => 0,
                'player_percentage' => 0,
                'tie_percentage' => 0
            ]);
        }

        $deckIds = $decks->pluck('id')->toArray();
        
        // 统计结果
        $stats = $this->lotteryLogService->mapper->getModel()
            ->whereIn('terrace_deck_id', $deckIds)
            ->whereNotNull('transformationResult')
            ->selectRaw('
                COUNT(*) as total,
                SUM(CASE WHEN transformationResult = "1" THEN 1 ELSE 0 END) as banker_wins,
                SUM(CASE WHEN transformationResult = "2" THEN 1 ELSE 0 END) as player_wins,
                SUM(CASE WHEN transformationResult = "3" THEN 1 ELSE 0 END) as ties
            ')
            ->first();

        $total = $stats->total ?: 1;
        
        return $this->success([
            'total' => $stats->total,
            'banker_wins' => $stats->banker_wins,
            'player_wins' => $stats->player_wins,
            'ties' => $stats->ties,
            'banker_percentage' => round(($stats->banker_wins / $total) * 100, 2),
            'player_percentage' => round(($stats->player_wins / $total) * 100, 2),
            'tie_percentage' => round(($stats->ties / $total) * 100, 2)
        ]);
    }
}