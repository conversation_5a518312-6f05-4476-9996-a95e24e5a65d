name: PHPUnit for MineAdmin

on:
  push:
  pull_request:
  schedule:
    - cron: '0 2 * * *'

jobs:
  cs-fix:
    name: PHP CS Fix on PHP${{ matrix.php }} ${{ matrix.swoole }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        os: [ ubuntu-latest ]
        php: [ '8.1','8.2','8.3' ]
        swoole: [ 'swoole']
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          tools: php-cs-fixer
          extensions: redis, pdo, pdo_mysql, bcmath, ${{ matrix.swoole }}
      - name: Setup Packages
        run: composer update -oW
      - name: Run CS Fix
        run: |
          vendor/bin/php-cs-fixer fix app --dry-run --diff
          vendor/bin/php-cs-fixer fix api --dry-run --diff
  tests:
    needs: cs-fix
    name: Test on PHP${{ matrix.php-version }} Swoole-${{ matrix.sw-version }}
    runs-on: "${{ matrix.os }}"
    strategy:
      matrix:
        os: [ ubuntu-latest ]
        php-version: [ '8.3', '8.2', '8.1' ]
        sw-version: [ 'v5.0.3', 'v5.1.2', 'master' ]
        exclude:
          - php-version: '8.3'
            sw-version: 'v5.0.3'
      max-parallel: 20
      fail-fast: false
    env:
      SW_VERSION: ${{ matrix.sw-version }}
      MYSQL_VERSION: '8.0'
      PGSQL_VERSION: '14'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Upgrade
        run: |
          sudo apt-get clean
          sudo apt-get update
          sudo apt-get upgrade -f
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}
          tools: phpize
          extensions: redis, pdo, pdo_mysql, bcmath
          ini-values: opcache.enable_cli=0
      - name: Setup Swoole
        run: |
          sudo apt-get install libcurl4-openssl-dev libc-ares-dev libpq-dev
          wget https://github.com/swoole/swoole-src/archive/${SW_VERSION}.tar.gz -O swoole.tar.gz
          mkdir -p swoole
          tar -xf swoole.tar.gz -C swoole --strip-components=1
          rm swoole.tar.gz
          cd swoole
          phpize
          ./configure --enable-openssl --enable-swoole-curl --enable-cares --enable-swoole-pgsql --enable-brotli
          make -j$(nproc)
          sudo make install
          sudo sh -c "echo extension=swoole > /etc/php/${{ matrix.php-version }}/cli/conf.d/swoole.ini"
          sudo sh -c "echo swoole.use_shortname='Off' >> /etc/php/${{ matrix.php-version }}/cli/conf.d/swoole.ini"
          php --ri swoole
      - name: Setup Packages
        run: ./.travis/requirement.install.sh
      - name: Run PHPStan
        run: ./.travis/run.check.sh
      - name: Setup Services
        run: ./.travis/setup.services.sh
      - name: Setup Mysql
        run: export TRAVIS_BUILD_DIR=$(pwd) && bash ./.travis/setup.mysql.sh
      - name: Setup PostgreSQL
        run: export TRAVIS_BUILD_DIR=$(pwd) && bash ./.travis/setup.pgsql.sh
      - name: Run Scripts Before Test
        run: cp .travis/.env.example .env
      - name: Print PHP Environments
        run: |
          php -i
          php -m
      - name: Run Mysql Test Cases
        env:
          DB_DRIVER: mysql
          DB_HOST: 127.0.0.1
          DB_DATABASE: mineadmin
        run: ./.travis/run.test.sh
      - name: Run PgSql Test Cases
        env:
          DB_DRIVER: pgsql
          DB_HOST: 127.0.0.1
          DB_PORT: 5432
          DB_USERNAME: postgres
          DB_PASSWORD: postgres
          DB_CHARSET: utf8
          DB_DATABASE: mineadmin
        run: ./.travis/run.test.sh