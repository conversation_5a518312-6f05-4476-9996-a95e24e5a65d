# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Baccarat simulation and betting management system built on top of MineAdmin (a Hyperf-based admin framework). The system simulates baccarat games, manages betting strategies, and tracks statistics.

### Tech Stack
- **Backend**: PHP 8.1+, Hyperf 3.1 framework (Swoole-based)
- **Frontend**: Vue 3 + Vite + Arco Design
- **Database**: MySQL 8.0+
- **Cache**: Redis 4.0+
- **Container**: Docker & Docker Compose

## Commands

### Development Server
```bash
# Start development server with hot reload
php watch -c

# Alternative: Start without hot reload
php bin/hyperf.php start
```

### Testing
```bash
# Run all tests using Pest
composer test

# Run tests with coverage
composer coverage

# Run specific test file
./vendor/bin/pest tests/Unit/Baccarat/LotteryResultTest.php
```

### Code Quality
```bash
# Fix code style issues
composer cs-fix

# Run static analysis
composer analyse
```

### Database
```bash
# Run migrations (MineAdmin uses migrate system, not SQL files)
php bin/hyperf.php migrate

# Create new migration
php bin/hyperf.php gen:migration create_table_name
```

### Frontend Development
```bash
# Navigate to frontend directory
cd mine-ui/

# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

### Docker Setup
```bash
# Start all services (MySQL, Redis, Nginx, Hyperf)
docker-compose up -d

# View logs
docker-compose logs -f hyperf

# Stop services
docker-compose down
```

## Architecture

### Directory Structure
- **app/Baccarat/**: Core baccarat business logic
  - `Controller/`: HTTP controllers for baccarat features
  - `Service/`: Business logic services (betting, rules, statistics)
  - `Model/`: Eloquent models for database entities
  - `Mapper/`: Data mappers for model operations
  - `Database/Migrations/`: Database migration files
  - `Service/BettingAmountStrategy/`: Betting strategy implementations (Martingale, Flat, etc.)
  - `Service/Prediction/`: Monte Carlo prediction engine
  - `Service/Websocket/`: WebSocket client for real-time betting

- **app/System/**: MineAdmin system modules (users, permissions, etc.)
- **app/Setting/**: System settings and configuration management
- **api/**: API controllers and middleware
- **mine-ui/**: Vue 3 frontend application
- **config/**: Hyperf configuration files
- **runtime/**: Cache, logs, and compiled proxies

### Key Services

1. **BaccaratService**: Main service orchestrating betting operations
2. **BaccaratSimulatedBettingService**: Handles simulated betting logic
3. **BaccaratTerraceDeckService**: Manages game decks and shoes
4. **BettingAnalysisService**: Analyzes betting patterns and results
5. **WebSocketManageService**: Manages WebSocket connections for real-time data
6. **PredictionGateway**: Monte Carlo simulation for outcome prediction

### Database Schema
The system uses migrations to manage database schema. Key tables include:
- `baccarat_terrace`: Gaming tables
- `baccarat_terrace_deck`: Game shoes/decks
- `baccarat_lottery_log`: Game results history
- `baccarat_simulated_betting`: Betting configurations
- `baccarat_simulated_betting_log`: Betting history
- `baccarat_betting_rule`: Betting rules and strategies
- `baccarat_deck_statistics`: Statistical analysis data

### Betting Strategies
The system implements multiple betting strategies:
- Fixed Ratio Strategy
- Flat Betting (Fixed amount)
- Layered Strategy
- Martingale Strategy
- 1-3-2-6 Strategy

## Important Patterns

### Service Container
Hyperf uses dependency injection extensively. Services are injected via constructor:
```php
public function __construct(
    protected BaccaratTerraceService $terraceService
) {}
```

### Async Processing
The system uses Swoole coroutines for async operations. WebSocket connections and long-running processes are handled asynchronously.

### Event System
Uses event listeners for betting events:
- `BettingEvent`: Triggered when betting occurs
- `WaitingEvent`: Handles waiting sequences
- `RecvMessageEvent`: Processes incoming WebSocket messages

### Data Transfer Objects (DTOs)
DTOs are used for data validation and transfer between layers.

## Development Notes

1. **Swoole Extension Required**: Ensure Swoole extension is installed and Short Name is disabled
2. **Memory Limits**: The system sets memory_limit to 1G for PHP processes
3. **Hot Reload**: Use `php watch -c` for development with auto-reload
4. **Proxy Classes**: Runtime proxy classes are generated in `runtime/container/proxy/`
5. **Logging**: Logs are stored in `runtime/logs/` organized by module and date
6. **WebSocket**: Real-time betting uses WebSocket connections to external services