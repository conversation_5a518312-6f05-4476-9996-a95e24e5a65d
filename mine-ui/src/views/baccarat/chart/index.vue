<template>
  <div class="ma-content-block lg:flex justify-between p-4">
    <!-- 左侧控制面板 -->
    <div class="lg:w-64 w-full mb-4 lg:mb-0 lg:mr-4">
      <a-form :model="queryParams" @submit="handleSearch" layout="vertical">
        <a-form-item field="date" label="日期" :rules="[{ required: true, message: '请选择日期' }]">
          <a-date-picker v-model="queryParams.date" style="width: 100%" placeholder="请选择日期" />
        </a-form-item>
        <a-form-item field="terrace_id" label="房间" :rules="[{ required: true, message: '请选择房间' }]">
          <a-select v-model="queryParams.terrace_id" placeholder="请选择房间" style="width: 100%">
            <a-option v-for="room in rooms" :key="room.id" :value="room.id" :label="room.title">
              {{ room.title }}
            </a-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit" style="width: 100%">查询</a-button>
        </a-form-item>
      </a-form>
      
      <!-- 统计信息 -->
      <div v-if="statistics.total > 0" class="mt-4 p-4 bg-white rounded shadow">
        <h3 class="text-lg font-bold mb-3">统计信息</h3>
        <div class="space-y-2">
          <div class="flex justify-between">
            <span>总局数:</span>
            <span class="font-semibold">{{ statistics.total }}</span>
          </div>
          <div class="flex justify-between">
            <span>庄胜:</span>
            <span class="text-red-600 font-semibold">
              {{ statistics.banker_wins }} ({{ statistics.banker_percentage }}%)
            </span>
          </div>
          <div class="flex justify-between">
            <span>闲胜:</span>
            <span class="text-blue-600 font-semibold">
              {{ statistics.player_wins }} ({{ statistics.player_percentage }}%)
            </span>
          </div>
          <div class="flex justify-between">
            <span>和局:</span>
            <span class="text-green-600 font-semibold">
              {{ statistics.ties }} ({{ statistics.tie_percentage }}%)
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧大路图显示区域 -->
    <div class="flex-1 bg-white rounded shadow p-4">
      <div class="mb-4 flex justify-between items-center">
        <h2 class="text-xl font-bold">百家乐大路图</h2>
        <a-space>
          <a-button @click="handleRefresh" :loading="loading">
            <template #icon><icon-refresh /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="handleExport">
            <template #icon><icon-download /></template>
            导出
          </a-button>
        </a-space>
      </div>

      <!-- 大路图画布 -->
      <div class="border border-gray-300 rounded p-2 overflow-auto" style="min-height: 400px;">
        <div v-if="loading" class="flex items-center justify-center h-96">
          <a-spin size="large" tip="加载中..." />
        </div>
        
        <div v-else-if="chartData.length === 0" class="flex items-center justify-center h-96 text-gray-500">
          <div class="text-center">
            <icon-empty size="64" />
            <p class="mt-4">暂无数据，请选择日期和房间后查询</p>
          </div>
        </div>

        <div v-else class="big-road-chart" ref="chartContainer">
          <svg :width="svgWidth" :height="svgHeight">
            <!-- 网格线 -->
            <g class="grid-lines">
              <line
                v-for="i in gridColumns"
                :key="`col-${i}`"
                :x1="i * cellSize"
                :y1="0"
                :x2="i * cellSize"
                :y2="svgHeight"
                stroke="#e0e0e0"
                stroke-width="1"
              />
              <line
                v-for="i in 6"
                :key="`row-${i}`"
                :x1="0"
                :y1="i * cellSize"
                :x2="svgWidth"
                :y2="i * cellSize"
                stroke="#e0e0e0"
                stroke-width="1"
              />
            </g>

            <!-- 结果圆圈 -->
            <g class="results">
              <g
                v-for="(item, index) in chartData"
                :key="index"
                :transform="`translate(${item.column * cellSize + cellSize/2}, ${item.row * cellSize + cellSize/2})`"
                @click="handleCellClick(item)"
                class="cursor-pointer"
              >
                <circle
                  :r="cellSize / 2 - 3"
                  :fill="getResultColor(item.result)"
                  :stroke="getResultStroke(item.result)"
                  stroke-width="2"
                />
                <text
                  x="0"
                  y="5"
                  text-anchor="middle"
                  :fill="getTextColor(item.result)"
                  font-size="16"
                  font-weight="bold"
                >
                  {{ item.result }}
                </text>
              </g>
            </g>
          </svg>
        </div>
      </div>

      <!-- 图例 -->
      <div class="mt-4 flex items-center justify-center space-x-6">
        <div class="flex items-center">
          <div class="w-6 h-6 rounded-full bg-red-100 border-2 border-red-600 mr-2"></div>
          <span>庄 (B)</span>
        </div>
        <div class="flex items-center">
          <div class="w-6 h-6 rounded-full bg-blue-100 border-2 border-blue-600 mr-2"></div>
          <span>闲 (P)</span>
        </div>
        <div class="flex items-center">
          <div class="w-6 h-6 rounded-full bg-green-100 border-2 border-green-600 mr-2"></div>
          <span>和 (T)</span>
        </div>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <a-modal v-model:visible="detailVisible" title="开奖详情" :footer="false">
      <a-descriptions :column="1" v-if="selectedItem">
        <a-descriptions-item label="期号">{{ selectedItem.issue }}</a-descriptions-item>
        <a-descriptions-item label="结果">
          <a-tag :color="getTagColor(selectedItem.result)">
            {{ getResultText(selectedItem.result) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="开奖时间">{{ selectedItem.created_at }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import baccaratChart from '@/api/baccarat/chart'

// 查询参数
const queryParams = reactive({
  date: new Date().toISOString().split('T')[0],
  terrace_id: null
})

// 数据状态
const loading = ref(false)
const rooms = ref([])
const chartData = ref([])
const statistics = ref({
  total: 0,
  banker_wins: 0,
  player_wins: 0,
  ties: 0,
  banker_percentage: 0,
  player_percentage: 0,
  tie_percentage: 0
})

// 图表配置
const cellSize = 30
const svgHeight = 200
const gridColumns = computed(() => Math.max(20, Math.ceil(chartData.value.length / 6) + 5))
const svgWidth = computed(() => gridColumns.value * cellSize)

// 详情弹窗
const detailVisible = ref(false)
const selectedItem = ref(null)

// 获取房间列表
const fetchRooms = async () => {
  try {
    const res = await baccaratChart.getRooms()
    if (res.code === 200) {
      rooms.value = res.data
    }
  } catch (error) {
    Message.error('获取房间列表失败')
  }
}

// 查询数据
const handleSearch = async () => {
  if (!queryParams.date || !queryParams.terrace_id) {
    Message.warning('请选择日期和房间')
    return
  }

  loading.value = true
  try {
    // 获取开奖结果
    const [resultRes, statsRes] = await Promise.all([
      baccaratChart.getLotteryResults(queryParams),
      baccaratChart.getStatistics(queryParams)
    ])

    if (resultRes.code === 200) {
      chartData.value = resultRes.data.chart_data || []
    }

    if (statsRes.code === 200) {
      statistics.value = statsRes.data
    }

    if (chartData.value.length === 0) {
      Message.info('该日期房间暂无开奖数据')
    }
  } catch (error) {
    Message.error('查询数据失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 刷新数据
const handleRefresh = () => {
  handleSearch()
}

// 导出数据
const handleExport = () => {
  // TODO: 实现导出功能
  Message.info('导出功能开发中...')
}

// 点击单元格
const handleCellClick = (item) => {
  selectedItem.value = item
  detailVisible.value = true
}

// 获取结果颜色
const getResultColor = (result) => {
  switch (result) {
    case 'B': return '#fef2f2'  // 庄 - 红色背景
    case 'P': return '#eff6ff'  // 闲 - 蓝色背景
    case 'T': return '#f0fdf4'  // 和 - 绿色背景
    default: return '#f9fafb'
  }
}

// 获取结果边框颜色
const getResultStroke = (result) => {
  switch (result) {
    case 'B': return '#dc2626'  // 庄 - 红色边框
    case 'P': return '#2563eb'  // 闲 - 蓝色边框
    case 'T': return '#16a34a'  // 和 - 绿色边框
    default: return '#6b7280'
  }
}

// 获取文字颜色
const getTextColor = (result) => {
  switch (result) {
    case 'B': return '#dc2626'  // 庄 - 红色
    case 'P': return '#2563eb'  // 闲 - 蓝色
    case 'T': return '#16a34a'  // 和 - 绿色
    default: return '#6b7280'
  }
}

// 获取标签颜色
const getTagColor = (result) => {
  switch (result) {
    case 'B': return 'red'
    case 'P': return 'blue'
    case 'T': return 'green'
    default: return 'gray'
  }
}

// 获取结果文字
const getResultText = (result) => {
  switch (result) {
    case 'B': return '庄'
    case 'P': return '闲'
    case 'T': return '和'
    default: return result
  }
}

// 组件挂载
onMounted(() => {
  fetchRooms()
})
</script>

<style scoped>
.big-road-chart {
  position: relative;
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
}

.big-road-chart svg {
  min-width: 100%;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-pointer:hover {
  opacity: 0.8;
}
</style>