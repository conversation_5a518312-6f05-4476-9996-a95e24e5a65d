import { request } from '@/utils/request.js'

/**
 * 百家乐图表相关接口
 */
export default {
  /**
   * 获取房间列表
   * @returns {Promise}
   */
  getRooms() {
    return request({
      url: 'baccarat/chart/rooms',
      method: 'get'
    })
  },

  /**
   * 获取开奖结果
   * @param {Object} params - 查询参数
   * @param {string} params.date - 日期
   * @param {number} params.terrace_id - 房间ID
   * @returns {Promise}
   */
  getLotteryResults(params) {
    return request({
      url: 'baccarat/chart/lottery-results',
      method: 'get',
      params
    })
  },

  /**
   * 获取统计信息
   * @param {Object} params - 查询参数
   * @param {string} params.date - 日期
   * @param {number} params.terrace_id - 房间ID
   * @returns {Promise}
   */
  getStatistics(params) {
    return request({
      url: 'baccarat/chart/statistics',
      method: 'get',
      params
    })
  },

  /**
   * 导出数据
   * @param {Object} params - 导出参数
   * @returns {Promise}
   */
  exportData(params) {
    return request({
      url: 'baccarat/chart/export',
      method: 'post',
      data: params,
      responseType: 'blob'
    })
  }
}