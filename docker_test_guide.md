# Docker 环境下测试新缓存策略指南

## 环境说明

您的环境: WSL Docker + docker-compose.yml

## 测试步骤

### 1. 进入 Docker 容器

```bash
# 方式一：进入hyperf容器
docker-compose exec hyperf bash

# 方式二：直接执行命令
docker-compose exec hyperf php bin/hyperf.php calculate:lottery-results-based-lottery-log --sequence="bpttbbpbpbbbpppppbbbbbbpppbpbppbbpbppbbpbpbpppbbpbtbtbbpppt" --mine-confidence=60
```

### 2. 验证缓存策略修复

#### 测试命令组合

```bash
# 测试置信度60%
docker-compose exec hyperf php bin/hyperf.php calculate:lottery-results-based-lottery-log --sequence="bpttbbpbpbbbpppppbbbbbbpppbpbppbbpbppbbpbpbpppbbpbtbtbbpppt" --mine-confidence=60

# 测试置信度70%
docker-compose exec hyperf php bin/hyperf.php calculate:lottery-results-based-lottery-log --sequence="bpttbbpbpbbbpppppbbbbbbpppbpbppbbpbppbbpbpbpppbbpbtbtbbpppt" --mine-confidence=70

# 测试置信度80%
docker-compose exec hyperf php bin/hyperf.php calculate:lottery-results-based-lottery-log --sequence="bpttbbpbpbbbpppppbbbbbbpppbpbppbbpbppbbpbpbpppbbpbtbtbbpppt" --mine-confidence=80
```

#### 使用详细输出模式验证缓存

```bash
# 第一次执行（应该显示API调用）
docker-compose exec hyperf php bin/hyperf.php calculate:lottery-results-based-lottery-log --sequence="bpttbbpbpbbbpppppbbbbbbpppbpbppbbpbpbpppbbpbtbtbbpppt" --mine-confidence=70 --verbose-output

# 第二次执行（应该显示使用缓存）
docker-compose exec hyperf php bin/hyperf.php calculate:lottery-results-based-lottery-log --sequence="bpttbbpbpbbbpppppbbbbbbpppbpbppbbpbppbbpbpbpppbbpbtbtbbpppt" --mine-confidence=70 --verbose-output
```

### 3. 运行自动化测试脚本

```bash
# 运行测试脚本
docker-compose exec hyperf php test_cache_strategy.php
```

### 4. 测试缓存清除功能

```bash
# 清除特定序列的缓存
docker-compose exec hyperf php bin/hyperf.php calculate:lottery-results-based-lottery-log --sequence="bpttbbpbpbbbpppppbbbbbbpppbpbppbbpbppbbpbpbpppbbpbtbtbbpppt" --mine-confidence=70 --clear-cache

# 再次执行应该重新获取预测数据
docker-compose exec hyperf php bin/hyperf.php calculate:lottery-results-based-lottery-log --sequence="bpttbbpbpbbbpppppbbbbbbpppbpbppbbpbppbbpbpbpppbbpbtbtbbpppt" --mine-confidence=70 --verbose-output
```

## 预期结果

### 修复后的正确行为

1. **置信度阈值越低，投注次数越多**

   - 60% 阈值：应该有最多的投注次数
   - 70% 阈值：中等投注次数
   - 80% 阈值：最少的投注次数

2. **相同序列的预测数据被缓存和复用**

   - 第一次执行显示"🌐 开始获取预测数据..."
   - 第二次执行显示"💾 使用缓存的预测数据"

3. **投注决策动态计算**
   - 不同置信度下，相同序列位置的预测置信度相同
   - 但投注决策根据当前阈值动态计算

### 验证要点

- **逻辑一致性**：置信度 60%的投注次数 >= 置信度 70%的投注次数 >= 置信度 80%的投注次数
- **缓存效率**：第二次执行相同序列时显示缓存命中率 100%
- **数据一致性**：相同序列位置的预测置信度在所有阈值下保持一致

## 故障排除

### 如果容器未启动

```bash
# 启动所有服务
docker-compose up -d

# 检查容器状态
docker-compose ps
```

### 如果 Redis 未连接

检查 `.env` 文件中的 Redis 配置，确保指向正确的 Redis 服务。

### 如果缓存清除失败

```bash
# 进入Redis容器清除缓存
docker-compose exec redis redis-cli
> FLUSHDB
> exit
```
