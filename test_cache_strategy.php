<?php

/**
 * 缓存策略测试脚本
 * 
 * 此脚本用于测试新的缓存机制，验证相同序列在不同置信度阈值下的行为
 * 运行方式：
 * 1. 在Docker容器中执行: docker-compose exec hyperf php test_cache_strategy.php
 * 2. 或者直接在宿主机执行: php test_cache_strategy.php
 */

echo "=== 百家乐投注分析缓存策略测试 ===\n\n";

$testSequence = "bpttbbpbpbbbpppppbbbbbbpppbpbppbbpbppbbpbpbpppbbpbtbtbbpppt";
$confidenceThresholds = [60, 70, 80];

echo "测试序列: $testSequence\n\n";

foreach ($confidenceThresholds as $confidence) {
    echo "=== 置信度阈值: $confidence% ===\n";

    // 构建命令
    $command = "php bin/hyperf.php calculate:lottery-results-based-lottery-log " .
        "--sequence=\"$testSequence\" " .
        "--mine-confidence=$confidence";

    echo "执行命令: $command\n";
    echo "结果:\n";

    // 执行命令并捕获输出
    $output = shell_exec($command);

    // 提取投注结果
    if (preg_match('/投注结果: (\[.*?\])/', $output, $matches)) {
        $bettingResults = $matches[1];
        $results = json_decode($bettingResults);

        echo "投注结果: $bettingResults\n";
        echo "投注次数: " . count($results) . "\n";

        // 计算连败情况
        $maxConsecutiveLosses = 0;
        $currentConsecutiveLosses = 0;

        foreach ($results as $result) {
            if ($result == "0") {
                $currentConsecutiveLosses++;
                $maxConsecutiveLosses = max($maxConsecutiveLosses, $currentConsecutiveLosses);
            } else {
                $currentConsecutiveLosses = 0;
            }
        }

        echo "最大连败次数: $maxConsecutiveLosses\n";

        // 计算胜率
        $wins = count(array_filter($results, fn($r) => $r == "1"));
        $winRate = count($results) > 0 ? round($wins / count($results) * 100, 2) : 0;
        echo "胜率: $winRate%\n";
    } else {
        echo "未能解析投注结果\n";
        echo "完整输出:\n$output\n";
    }

    echo "\n" . str_repeat("-", 50) . "\n\n";
}

echo "=== 缓存机制验证要点 ===\n";
echo "1. 置信度阈值越低，投注次数应该越多\n";
echo "2. 相同序列的预测数据应该被缓存，不同置信度下重复使用\n";
echo "3. 投注决策基于缓存的预测数据和当前置信度阈值动态计算\n";
echo "4. 第二次执行相同序列应该显示使用缓存数据\n\n";

echo "=== 测试完成 ===\n";
