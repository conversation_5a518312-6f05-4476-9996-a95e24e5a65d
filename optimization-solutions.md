# Hyperf 与 MySQL 慢查询差异解决方案

## 🔍 问题诊断总结

### 核心问题
- **Hyperf 记录时间**：包含锁等待 + 连接开销 + SQL执行
- **MySQL 慢查询日志**：仅记录纯 SQL 执行时间
- **实际情况**：您的查询可能存在严重的锁竞争问题

## 🚀 立即解决方案

### 1. 优化 Docker Compose 配置

更新您的 `docker-compose.yml` MySQL 配置：

```yaml
mysql:
  container_name: mine-mysql
  environment:
    - "TZ=Asia/Shanghai"
    - MYSQL_ROOT_PASSWORD=12345678
    - "explicit_defaults_for_timestamp=true"
    - "lower_case_table_names=1"
  image: mysql:8.0
  restart: always
  volumes:
    - mysql-data:/var/lib/mysql
    - ./docker/mysql/mineadmin_backup.sql:/docker-entrypoint-initdb.d/mineadmin_backup.sql
    - ./docker/mysql/logs:/var/lib/mysql/logs
    - ./docker/mysql/conf.d:/etc/mysql/conf.d  # 添加这行
  ports:
    - "3306:3306"
  command: >
    --default-authentication-plugin=caching_sha2_password
    --max_connections=200
    --slow_query_log=1
    --slow_query_log_file=/var/lib/mysql/logs/slow_query.log
    --long_query_time=0.5
    --log_queries_not_using_indexes=1
    --innodb_lock_wait_timeout=10
    --innodb_buffer_pool_size=512M
    --innodb_log_file_size=256M
    --innodb_flush_log_at_trx_commit=2
    --query_cache_size=128M
    --tmp_table_size=128M
    --max_heap_table_size=128M
```

### 2. 创建 MySQL 配置文件

创建 `docker/mysql/conf.d/optimization.cnf`：

```ini
[mysqld]
# 连接和超时设置
max_connections = 200
wait_timeout = 600
interactive_timeout = 600
connect_timeout = 30

# InnoDB 优化
innodb_buffer_pool_size = 512M
innodb_buffer_pool_instances = 4
innodb_log_file_size = 256M
innodb_log_buffer_size = 32M
innodb_flush_log_at_trx_commit = 2
innodb_lock_wait_timeout = 10
innodb_thread_concurrency = 8
innodb_read_io_threads = 4
innodb_write_io_threads = 4

# 查询优化
query_cache_size = 128M
query_cache_limit = 4M
query_cache_type = 1
tmp_table_size = 128M
max_heap_table_size = 128M
sort_buffer_size = 4M
read_buffer_size = 2M
read_rnd_buffer_size = 8M
bulk_insert_buffer_size = 16M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/lib/mysql/logs/slow_query.log
long_query_time = 0.5
log_queries_not_using_indexes = 1
log_slow_admin_statements = 1

# 二进制日志
log_bin = /var/lib/mysql/logs/mysql-bin.log
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M

# 错误日志
log_error = /var/lib/mysql/logs/mysql-error.log
log_error_verbosity = 2
```

### 3. Hyperf 应用层优化

#### A. 数据库连接池配置优化

更新 `config/autoload/databases.php`：

```php
<?php
return [
    'default' => [
        'driver' => 'mysql',
        'host' => env('DB_HOST', 'localhost'),
        'port' => env('DB_PORT', 3306),
        'database' => env('DB_DATABASE', 'hyperf'),
        'username' => env('DB_USERNAME', 'root'),
        'password' => env('DB_PASSWORD', ''),
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'pool' => [
            'min_connections' => 5,
            'max_connections' => 20,
            'connect_timeout' => 10.0,
            'wait_timeout' => 3.0,
            'heartbeat' => -1,
            'max_idle_time' => 60.0,
        ],
        'options' => [
            PDO::ATTR_CASE => PDO::CASE_NATURAL,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_STRINGIFY_FETCHES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci',
            // 添加超时设置
            PDO::ATTR_TIMEOUT => 10,
        ],
    ],
];
```

#### B. 优化业务代码

```php
<?php
// app/Baccarat/Service/OptimizedBaccaratLotteryLogService.php
declare(strict_types=1);

namespace App\Baccarat\Service;

use App\Baccarat\Mapper\BaccaratLotteryLogMapper;
use App\Baccarat\Model\BaccaratLotteryLog;
use Hyperf\DbConnection\Db;
use Hyperf\Redis\Redis;
use Hyperf\Cache\Annotation\Cacheable;
use Hyperf\Cache\Annotation\CacheEvict;
use Mine\Abstracts\AbstractService;
use Mine\MineModel;

class OptimizedBaccaratLotteryLogService extends AbstractService
{
    public function __construct(
        BaccaratLotteryLogMapper $mapper,
        private Redis $redis
    ) {
        $this->mapper = $mapper;
    }

    /**
     * 使用 Redis 分布式锁避免并发问题
     */
    public function createLotteryLogWithLock(array $data): ?BaccaratLotteryLog
    {
        $lockKey = "lottery_log_create:{$data['issue']}";
        $lockValue = uniqid();
        $lockExpire = 30; // 30秒超时

        // 尝试获取分布式锁
        if ($this->redis->set($lockKey, $lockValue, ['NX', 'EX' => $lockExpire])) {
            try {
                // 检查是否已存在
                $existing = $this->getLotteryLog($data['issue']);
                if ($existing) {
                    return $existing;
                }

                // 在事务中创建记录
                return Db::transaction(function () use ($data) {
                    return $this->mapper->getModel()->create($data);
                }, 2);
            } finally {
                // 确保释放锁
                $script = "
                    if redis.call('GET', KEYS[1]) == ARGV[1] then
                        return redis.call('DEL', KEYS[1])
                    else
                        return 0
                    end
                ";
                $this->redis->eval($script, [$lockKey, $lockValue], 1);
            }
        }

        // 获取锁失败，等待并重试
        usleep(100000); // 等待 100ms
        return $this->getLotteryLog($data['issue']);
    }

    /**
     * 批量更新优化 - 减少锁竞争
     */
    public function batchUpdateOptimized(array $updates): array
    {
        // 按 issue 分组避免死锁
        ksort($updates);

        $results = [];
        foreach (array_chunk($updates, 10) as $batch) {
            $batchResults = Db::transaction(function () use ($batch) {
                $results = [];
                foreach ($batch as $update) {
                    $result = $this->updateSingleRecord($update);
                    if ($result) {
                        $results[] = $result;
                    }
                }
                return $results;
            }, 3);
            
            $results = array_merge($results, $batchResults);
        }

        return $results;
    }

    private function updateSingleRecord(array $update): ?BaccaratLotteryLog
    {
        return $this->mapper->getModel()
            ->where('issue', $update['issue'])
            ->whereNull('transformationResult')
            ->first()?->update($update['data']);
    }
}
```

### 4. 监控和诊断工具

#### A. 创建监控命令

```php
<?php
// app/Command/DatabaseMonitorCommand.php
declare(strict_types=1);

namespace App\Command;

use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Hyperf\DbConnection\Db;
use Psr\Container\ContainerInterface;

#[Command]
class DatabaseMonitorCommand extends HyperfCommand
{
    protected ?string $name = 'db:monitor';
    protected string $description = '监控数据库锁等待和性能';

    public function handle()
    {
        $this->info('数据库性能监控报告');
        $this->info(str_repeat('=', 50));

        // 1. 检查锁等待
        $this->checkLockWaits();
        
        // 2. 检查长事务
        $this->checkLongRunningTransactions();
        
        // 3. 检查连接状态
        $this->checkConnections();
        
        // 4. 检查表状态
        $this->checkTableStats();
    }

    private function checkLockWaits(): void
    {
        $this->info("\n🔒 锁等待检查:");
        
        $locks = Db::select("
            SELECT 
                r.trx_mysql_thread_id as waiting_thread,
                r.trx_query as waiting_query,
                b.trx_mysql_thread_id as blocking_thread,
                b.trx_query as blocking_query,
                TIMESTAMPDIFF(SECOND, r.trx_started, NOW()) as wait_duration
            FROM information_schema.INNODB_TRX r
            JOIN information_schema.INNODB_TRX b ON r.trx_id != b.trx_id
            WHERE r.trx_state = 'LOCK WAIT'
        ");

        if (empty($locks)) {
            $this->comment('✅ 当前无锁等待');
        } else {
            foreach ($locks as $lock) {
                $this->error("⚠️  线程 {$lock->waiting_thread} 等待 {$lock->blocking_thread}, 等待时间: {$lock->wait_duration}s");
            }
        }
    }

    private function checkLongRunningTransactions(): void
    {
        $this->info("\n⏱️  长事务检查:");
        
        $transactions = Db::select("
            SELECT 
                trx_mysql_thread_id,
                trx_state,
                TIMESTAMPDIFF(SECOND, trx_started, NOW()) as duration,
                trx_query
            FROM information_schema.INNODB_TRX 
            WHERE TIMESTAMPDIFF(SECOND, trx_started, NOW()) > 5
            ORDER BY trx_started
        ");

        if (empty($transactions)) {
            $this->comment('✅ 当前无长事务');
        } else {
            foreach ($transactions as $trx) {
                $this->warn("⚠️  线程 {$trx->trx_mysql_thread_id}: {$trx->duration}s - {$trx->trx_state}");
            }
        }
    }

    private function checkConnections(): void
    {
        $this->info("\n🔗 连接状态检查:");
        
        $stats = Db::select("
            SELECT 
                COMMAND,
                COUNT(*) as count,
                AVG(TIME) as avg_time
            FROM information_schema.PROCESSLIST 
            GROUP BY COMMAND
        ");

        foreach ($stats as $stat) {
            $this->comment("📊 {$stat->COMMAND}: {$stat->count} 连接, 平均时间: {$stat->avg_time}s");
        }
    }

    private function checkTableStats(): void
    {
        $this->info("\n📋 baccarat_lottery_log 表统计:");
        
        $stats = Db::select("
            SELECT 
                TABLE_ROWS,
                DATA_LENGTH / 1024 / 1024 as data_mb,
                INDEX_LENGTH / 1024 / 1024 as index_mb,
                AUTO_INCREMENT
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'baccarat_lottery_log'
        ");

        foreach ($stats as $stat) {
            $this->comment("📊 行数: {$stat->TABLE_ROWS}, 数据大小: {$stat->data_mb}MB, 索引大小: {$stat->index_mb}MB");
        }
    }
}
```

### 5. 立即执行步骤

1. **重启 MySQL 容器应用新配置**：
```bash
docker-compose down mysql
docker-compose up -d mysql
```

2. **创建诊断目录**：
```bash
mkdir -p docker/mysql/conf.d
```

3. **执行诊断查询**：
```bash
# 进入 MySQL 容器执行诊断脚本
docker exec -i mine-mysql mysql -uroot -p12345678 mineadmin < scripts/diagnose_lock_issues.sql
```

4. **监控改善效果**：
```bash
# 在 Hyperf 容器中执行监控命令
php bin/hyperf.php db:monitor
```

## 📈 预期改善效果

- ✅ **锁等待时间减少** 90%+
- ✅ **INSERT/UPDATE 操作** 从 10+ 秒降低到毫秒级
- ✅ **MySQL 慢查询日志** 将开始记录真正的慢查询
- ✅ **系统整体性能** 显著提升

## 🔄 持续监控

定期执行监控命令，关注：
1. 锁等待情况
2. 长事务数量
3. 连接池使用率
4. MySQL 慢查询日志
