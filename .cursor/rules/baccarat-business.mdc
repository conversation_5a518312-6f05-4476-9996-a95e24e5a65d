---
description: 
globs: 
alwaysApply: false
---
# 百家乐游戏管理系统业务指南

## 业务概述
百家乐游戏管理系统是本项目的核心业务模块，包含游戏规则管理、投注策略、统计分析、模拟投注等功能。

## 业务模块结构

### 前端视图模块 (`mine-ui/src/views/baccarat/`)
- `betting-strategies/` - 投注策略管理
- `statistics/` - 游戏统计分析
- `terrace/` - 游戏平台管理
- `terraceDeck/` - 牌桌管理
- `rule/` - 游戏规则配置
- `simulatedBetting/` - 模拟投注功能
- `simulatedBettingLog/` - 模拟投注日志
- `simulatedBettingRule/` - 模拟投注规则
- `waitingSequence/` - 等待序列管理

### API 接口模块 (`mine-ui/src/api/baccarat/`)
- [baccaratStatistics.js](mdc:mine-ui/src/api/baccarat/baccaratStatistics.js) - 统计数据接口
- [baccaratTerrace.js](mdc:mine-ui/src/api/baccarat/baccaratTerrace.js) - 游戏平台接口
- [baccaratTerraceDeck.js](mdc:mine-ui/src/api/baccarat/baccaratTerraceDeck.js) - 牌桌管理接口
- [baccaratRule.js](mdc:mine-ui/src/api/baccarat/baccaratRule.js) - 游戏规则接口
- [baccaratSimulatedBetting.js](mdc:mine-ui/src/api/baccarat/baccaratSimulatedBetting.js) - 模拟投注接口
- [baccaratSimulatedBettingRule.js](mdc:mine-ui/src/api/baccarat/baccaratSimulatedBettingRule.js) - 模拟投注规则接口
- [baccaratWaitingSequence.js](mdc:mine-ui/src/api/baccarat/baccaratWaitingSequence.js) - 等待序列接口
- [baccaratBettingRuleLog.js](mdc:mine-ui/src/api/baccarat/baccaratBettingRuleLog.js) - 投注规则日志接口
- [simulatedBettingLog.js](mdc:mine-ui/src/api/baccarat/simulatedBettingLog.js) - 模拟投注日志接口

### 后端业务逻辑 (`app/Baccarat/`)
- `Controller/` - 控制器层，处理 HTTP 请求
- `Service/` - 业务逻辑层
  - `BaccaratDealer/` - 发牌员服务
  - `BettingAmountStrategy/` - 投注金额策略
  - `Coordinates/` - 坐标系统
  - `RoomManager/` - 房间管理
  - `Rule/` - 规则引擎
  - `Sequence/` - 序列管理
  - `Statistics/` - 统计服务
  - `Websocket/` - WebSocket 服务
- `Model/` - 数据模型层
- `Event/` - 事件系统
- `Dto/` - 数据传输对象

## 核心业务概念

### 游戏平台 (Terrace)
- **定义**: 百家乐游戏的运营平台
- **功能**: 管理多个牌桌、配置游戏参数
- **状态**: 启用/禁用、维护模式等

### 牌桌 (TerraceDeck)
- **定义**: 具体的游戏桌台
- **功能**: 发牌、下注、结算
- **属性**: 最小/最大投注额、手续费率等

### 投注策略 (Betting Strategies)
- **马丁格尔策略**: 输了加倍，赢了重置
- **反马丁格尔策略**: 赢了加倍，输了重置
- **平注策略**: 固定金额投注
- **自定义策略**: 用户自定义的投注逻辑

### 等待序列 (Waiting Sequence)
- **定义**: 根据历史结果等待特定模式出现
- **类型**: 单跳、双跳、连庄、连闲等
- **触发条件**: 序列长度、概率阈值等

### 模拟投注 (Simulated Betting)
- **功能**: 基于历史数据模拟投注过程
- **参数**: 起始资金、投注策略、止损止盈
- **输出**: 盈亏统计、风险评估、策略效果

## 数据流向

### 实时游戏数据流
1. **数据源**: 第三方游戏平台 API
2. **数据收集**: WebSocket 实时接收游戏结果
3. **数据处理**: 解析、验证、存储游戏数据
4. **策略执行**: 根据配置的策略生成投注建议
5. **结果展示**: 通过 WebSocket 推送到前端界面

### 统计分析数据流
1. **历史数据**: 从数据库读取历史游戏记录
2. **数据聚合**: 按时间、平台、牌桌等维度聚合
3. **指标计算**: 胜率、盈亏率、连续性等指标
4. **可视化**: 使用图表展示统计结果

## 开发指导

### 新增投注策略
1. 在 `app/Baccarat/Service/BettingAmountStrategy/` 创建策略类
2. 实现 `BettingAmountStrategyInterface` 接口
3. 在前端 `betting-strategies/` 目录添加配置界面
4. 更新策略枚举和工厂类

### 新增等待序列类型
1. 在 `app/Baccarat/Service/Sequence/` 创建序列类
2. 实现序列检测逻辑
3. 在前端 `waitingSequence/` 目录添加配置选项
4. 更新序列类型枚举

### 统计指标扩展
1. 在 `app/Baccarat/Service/Statistics/` 添加计算逻辑
2. 更新数据库表结构（如需要）
3. 在前端 `statistics/` 目录添加展示组件
4. 配置图表可视化

## 注意事项

### 性能优化
- **缓存策略**: 使用 Redis 缓存热点数据
- **分页查询**: 大数据量统计使用分页
- **异步处理**: 耗时计算使用队列异步处理

### 数据一致性
- **事务处理**: 投注相关操作使用数据库事务
- **幂等性**: API 接口支持重复调用
- **数据校验**: 前后端双重数据验证

### 安全考虑
- **权限控制**: 基于角色的功能访问控制
- **数据加密**: 敏感数据传输加密
- **操作日志**: 记录所有关键操作日志
