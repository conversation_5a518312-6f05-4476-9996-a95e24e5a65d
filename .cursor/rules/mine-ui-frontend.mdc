---
description: 
globs: 
alwaysApply: false
---
# MineAdmin Vue 前端项目开发指南

## 项目概述
MineAdmin Vue 是一个基于 Vue 3 + Arco Design + Vite 的管理后台前端项目，主要用于百家乐游戏管理系统的前端界面。

## 技术栈
- **框架**: Vue 3 (Composition API)
- **UI库**: Arco Design Vue
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **国际化**: Vue I18n
- **样式**: Less + TailwindCSS
- **图表**: ECharts + Vue-ECharts
- **富文本编辑器**: TinyMCE + WangEditor

## 目录结构

### 核心目录说明
- [main.js](mdc:mine-ui/src/main.js) - 应用入口文件，配置全局组件和插件
- [App.vue](mdc:mine-ui/src/App.vue) - 根组件
- [package.json](mdc:mine-ui/package.json) - 项目依赖和脚本配置

### API 接口层 (`src/api/`)
- [common.js](mdc:mine-ui/src/api/common.js) - 通用接口
- [login.js](mdc:mine-ui/src/api/login.js) - 登录相关接口
- `baccarat/` - 百家乐游戏相关接口
- `setting/` - 系统设置相关接口
- `system/` - 系统管理相关接口

### 组件层 (`src/components/`)
所有自定义组件都以 `ma-` 前缀命名，表示 MineAdmin 组件：

**通用组件**:
- `ma-form/` - 表单组件
- `ma-crud/` - CRUD 操作组件
- `ma-icon/` - 图标组件
- `ma-upload/` - 文件上传组件

**图表组件**:
- `ma-charts/` - 基础图表组件
- `ma-charts-waiting-sequence-pie/` - 等待序列饼图
- `ma-charts-waiting-strategy/` - 等待策略图表
- `ma-scatter/` - 散点图组件

**编辑器组件**:
- `ma-editor/` - 富文本编辑器
- `ma-codeEditor/` - 代码编辑器
- `ma-wangEditor/` - WangEditor 编辑器

**用户界面组件**:
- `ma-user/` - 用户组件
- `ma-userInfo/` - 用户信息组件
- `ma-verifyCode/` - 验证码组件

### 视图层 (`src/views/`)
- [login.vue](mdc:mine-ui/src/views/login.vue) - 登录页面
- `baccarat/` - 百家乐游戏管理页面
- `dashboard/` - 仪表板页面
- `system/` - 系统管理页面
- `setting/` - 系统设置页面
- `userCenter/` - 用户中心页面

### 其他重要目录
- `router/` - 路由配置
- `store/` - Pinia 状态管理
- `i18n/` - 国际化配置（中文/英文）
- `utils/` - 工具函数
- `style/` - 样式文件
- `assets/` - 静态资源

## 开发规范

### 组件命名规范
1. **组件目录**: 使用 kebab-case 命名 (如 `ma-form-modal`)
2. **组件文件**: 使用 PascalCase 命名 (如 `FormModal.vue`)
3. **组件前缀**: 自定义组件统一使用 `ma-` 前缀

### API 接口规范
1. **按模块组织**: 每个业务模块有独立的 API 目录
2. **统一导出**: 使用 `export` 导出接口函数
3. **命名规范**: 使用 camelCase 命名接口函数

### 样式规范
1. **主题样式**: 使用 `@arco-themes/vue-mine-admin-v2` 主题
2. **全局样式**: 定义在 `style/` 目录下
3. **组件样式**: 使用 scoped 样式或 CSS Modules

### 状态管理规范
1. **使用 Pinia**: 替代 Vuex 进行状态管理
2. **模块化**: 按功能模块拆分 store
3. **类型安全**: 使用 TypeScript 定义状态类型

## 常用开发命令
```bash
# 开发环境启动
npm run dev

# 生产环境构建
npm run build

# 预览构建结果
npm run preview
```

## 全局配置
- **环境变量**: 使用 `.env` 文件配置
- **全局工具**: `$tool` 和 `$common` 挂载到全局
- **图标组件**: 自动注册 `assets/ma-icons/` 下的图标为全局组件

## 特色功能
1. **动态主题**: 支持多种皮肤主题切换
2. **国际化**: 支持中英文双语
3. **权限控制**: 基于角色的访问控制
4. **WebSocket**: 实时数据推送支持
5. **图表可视化**: 丰富的数据图表展示
