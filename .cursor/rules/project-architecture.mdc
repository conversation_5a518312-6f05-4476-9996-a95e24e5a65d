---
alwaysApply: true
---
# 百家乐游戏管理系统架构指南

## 项目概述
这是一个基于 Hyperf + Vue 3 的百家乐游戏管理系统，采用前后端分离架构，支持实时数据推送、策略分析和模拟投注功能。

## 技术架构

### 后端技术栈 (PHP)
- **框架**: Hyperf 3.x (基于 Swoole 的协程框架)
- **数据库**: MySQL 8.0 + Redis
- **消息队列**: AMQP (RabbitMQ)
- **WebSocket**: Hyperf WebSocket Server
- **文档**: Swagger API 文档
- **测试**: PHPUnit + Pest

### 前端技术栈 (JavaScript)
- **框架**: Vue 3 + Composition API
- **UI库**: Arco Design Vue
- **构建**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **国际化**: Vue I18n
- **图表**: ECharts

## 项目结构

### 后端结构 (`app/`)
```
app/
├── Baccarat/           # 百家乐业务模块
│   ├── Controller/     # 控制器
│   ├── Service/       # 业务逻辑
│   ├── Model/         # 数据模型
│   ├── Event/         # 事件
│   └── Dto/           # 数据传输对象
├── System/            # 系统管理模块
├── Setting/           # 配置管理模块
└── Command/           # 命令行工具
```

### 前端结构 (`mine-ui/src/`)
```
src/
├── api/               # API 接口层
├── components/        # 通用组件
├── views/            # 页面组件
├── router/           # 路由配置
├── store/            # 状态管理
├── utils/            # 工具函数
└── style/            # 样式文件
```

### API 接口层 (`api/`)
```
api/
├── InterfaceApi/v1/   # 外部接口
├── Request/v1/        # 请求验证
├── Middleware/        # 中间件
└── Listener/          # 监听器
```

## 核心模块说明

### 百家乐业务模块
- **位置**: [app/Baccarat/](mdc:app/Baccarat)
- **功能**: 游戏逻辑、投注策略、数据分析
- **关键服务**:
  - 发牌员服务 (BaccaratDealer)
  - 投注策略 (BettingAmountStrategy)
  - 房间管理 (RoomManager)
  - 统计分析 (Statistics)

### 系统管理模块
- **位置**: [app/System/](mdc:app/System)
- **功能**: 用户管理、权限控制、系统监控
- **关键功能**:
  - 用户认证和授权
  - 角色权限管理
  - 系统日志和监控

### 配置管理模块
- **位置**: [app/Setting/](mdc:app/Setting)
- **功能**: 系统配置、参数管理
- **配置类型**:
  - 游戏参数配置
  - 系统设置
  - 第三方集成配置

## 数据流架构

### 实时数据流
```
第三方平台 → WebSocket → Hyperf Server → Redis → 前端展示
```

### API 数据流
```
前端请求 → 路由 → 中间件 → 控制器 → 服务层 → 数据层 → 响应
```

### 事件驱动流
```
业务事件 → 事件监听器 → 异步处理 → 消息队列 → 后台任务
```

## 开发环境配置

### 环境变量配置
- **主配置**: [.env](mdc:.env) - 环境变量配置
- **示例配置**: [.env.example](mdc:.env.example) - 配置模板

### Docker 配置
- **Docker Compose**: [docker-compose.yml](mdc:docker-compose.yml) - 容器编排
- **Dockerfile**: [Dockerfile](mdc:Dockerfile) - 镜像构建
- **MySQL 配置**: `docker/mysql/` - 数据库初始化
- **Nginx 配置**: `docker/nginx-frontend/` - 前端代理

### 依赖管理
- **PHP 依赖**: [composer.json](mdc:composer.json) - Composer 包管理
- **Node.js 依赖**: [mine-ui/package.json](mdc:mine-ui/package.json) - NPM 包管理

## 部署架构

### 生产环境部署
1. **应用服务器**: Swoole + Hyperf
2. **Web 服务器**: Nginx (前端静态文件 + API 代理)
3. **数据库**: MySQL 主从复制
4. **缓存**: Redis 集群
5. **消息队列**: RabbitMQ 集群

### CI/CD 流程
- **GitHub Actions**: [.github/workflows/](mdc:.github/workflows) - 自动化构建
- **测试**: PHPUnit 单元测试和功能测试
- **代码质量**: PHP-CS-Fixer + PHPStan 静态分析

## 监控和日志

### 应用日志
- **位置**: `runtime/logs/`
- **分类**: 
  - `baccarat/` - 游戏业务日志
  - `debug/` - 调试日志
  - `sql/` - SQL 查询日志

### 性能监控
- **Hyperf**: 内置性能监控
- **Redis**: 缓存命中率监控
- **MySQL**: 慢查询监控

## 安全措施

### 身份认证
- **JWT**: 基于 Token 的身份认证
- **权限控制**: RBAC 角色权限模型
- **API 限流**: 接口访问频率限制

### 数据安全
- **数据加密**: 敏感数据加密存储
- **传输安全**: HTTPS + WSS 加密传输
- **访问控制**: IP 白名单 + 操作日志

## 开发规范

### 代码风格
- **PHP**: PSR-12 编码规范
- **JavaScript**: ESLint + Prettier
- **提交规范**: Conventional Commits

### 测试规范
- **单元测试**: 覆盖率 > 80%
- **功能测试**: 关键业务流程测试
- **性能测试**: 并发和压力测试

### 文档规范
- **API 文档**: Swagger 自动生成
- **代码注释**: PHPDoc + JSDoc
- **README**: 详细的部署和使用说明
